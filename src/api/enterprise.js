import request from '@/utils/request'

/**
 * 企业管理相关接口
 *
 * 注意：这些API路径需要与后端API路径保持一致
 * 在开发环境中，这些请求会被 mockInterceptor.js 拦截并返回模拟数据
 * 在生产环境中，这些请求会直接发送到真实后端API
 */

// 获取企业列表数据
export function getEnterpriseList(params) {
  return request({
    url: '/system/sysEnterprise/list',
    method: 'get',
    params
  })
}

// 获取企业详情
export function getEnterpriseDetail(id) {
  return request({
    url: `/enterprise/detail/${id}`,
    method: 'get'
  })
}

// 添加企业
export function addEnterprise(data) {
  return request({
    url: '/enterprise/add',
    method: 'post',
    data: data
  })
}



// 删除企业
export function deleteEnterprise(id) {
  return request({
    url: `/enterprise/delete/${id}`,
    method: 'delete'
  })
}

// 批量删除企业
export function batchDeleteEnterprise(ids) {
  return request({
    url: '/enterprise/batchDelete',
    method: 'delete',
    data: { ids }
  })
}

// 获取企业风险级别选项
export function getRiskLevelOptions() {
  return request({
    url: '/enterprise/riskLevelOptions',
    method: 'get'
  })
}

// 获取企业分类统计数据
export function getEnterpriseCategoryStats() {
  return request({
    url: '/system/sysEnterprise/stat',
    method: 'get'
  })
}

// 更新企业信息（用于编辑抽屉）
export function updateEnterprise(data) {
  return request({
    url: `/enterprise/${data.id}`,
    method: 'put',
    data
  })
}