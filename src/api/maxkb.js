import axios from 'axios'

// maxKB AI助手API配置
const MAXKB_CONFIG = {
  // 这里需要配置您的maxKB服务地址和API密钥
  baseURL: process.env.VUE_APP_MAXKB_BASE_URL,
  apiKey: process.env.VUE_APP_MAXKB_API_KEY || '',
  applicationId: process.env.VUE_APP_MAXKB_APPLICATION_ID || ''
}

// 创建专用于maxKB的axios实例，避免被系统拦截器影响
const maxkbRequest = axios.create({
  baseURL: MAXKB_CONFIG.baseURL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// maxKB专用请求拦截器
maxkbRequest.interceptors.request.use(config => {
  // 为maxKB API添加专用的Authorization头
  if (MAXKB_CONFIG.apiKey) {
    config.headers['Authorization'] = `${MAXKB_CONFIG.apiKey}`
  }

  const fullUrl = `${config.baseURL}${config.url}`
  console.log(`[maxKB API] 请求: ${config.method?.toUpperCase()} ${fullUrl}`)
  console.log(`[maxKB API] Authorization: ${MAXKB_CONFIG.apiKey}`)
  console.log(`[maxKB API] 请求数据:`, config.data)

  return config
}, error => {
  console.error('[maxKB API] 请求错误:', error)
  return Promise.reject(error)
})

// maxKB专用响应拦截器
maxkbRequest.interceptors.response.use(response => {
  const fullUrl = `${response.config.baseURL}${response.config.url}`
  console.log(`[maxKB API] 响应成功: ${response.status} ${fullUrl}`)
  console.log(`[maxKB API] 响应数据:`, response.data)
  return response.data
}, error => {
  const fullUrl = error.config ? `${error.config.baseURL}${error.config.url}` : '未知地址'
  console.error(`[maxKB API] 响应错误: ${fullUrl}`)
  console.error('[maxKB API] 错误详情:', error)

  if (error.response) {
    const { status, data, config } = error.response
    console.error(`[maxKB API] HTTP ${status} 错误:`)
    console.error(`[maxKB API] 接口地址: ${config.baseURL}${config.url}`)
    console.error(`[maxKB API] 请求方法: ${config.method?.toUpperCase()}`)
    console.error(`[maxKB API] 请求头:`, config.headers)
    console.error(`[maxKB API] 请求数据:`, config.data)
    console.error(`[maxKB API] 响应数据:`, data)

    if (status === 401) {
      throw new Error(`maxKB API认证失败 (${fullUrl})，请检查API Key`)
    } else if (status === 404) {
      throw new Error(`maxKB API端点不存在 (${fullUrl})`)
    } else if (status >= 500) {
      throw new Error(`maxKB服务器错误 (${status}) - ${fullUrl}`)
    } else {
      throw new Error(`maxKB API请求失败 (${status}) - ${fullUrl}: ${data?.message || data?.error || '未知错误'}`)
    }
  } else if (error.request) {
    console.error(`[maxKB API] 网络错误: 无法连接到 ${fullUrl}`)
    throw new Error(`无法连接到maxKB服务 (${fullUrl})`)
  } else {
    console.error(`[maxKB API] 请求配置错误:`, error.message)
    throw new Error(`maxKB API请求配置错误: ${error.message}`)
  }

  return Promise.reject(error)
})

/**
 * 步骤2: 打开会话，获取会话ID (根据maxKB官方文档)
 * @param {string} applicationId - 应用ID
 * @returns {Promise}
 */
export function openChat(applicationId) {
  return maxkbRequest({
    url: `/api/application/${applicationId}/chat/open`,
    method: 'get'
  })
}

/**
 * 步骤3: 发送消息到maxKB AI助手 (根据官方文档)
 * @param {Object} params
 * @param {string} params.chat_id - 会话ID (必填)
 * @param {string} params.message - 用户消息
 * @param {boolean} params.re_chat - 是否重新对话
 * @param {boolean} params.stream - 是否流式输出
 * @returns {Promise}
 */
export function sendMessage(params) {
  if (!params.chat_id) {
    const error = new Error('chat_id is required')
    console.error('[maxKB API] sendMessage 参数错误:', error.message)
    throw error
  }

  const requestData = {
    message: params.message || '',
    re_chat: params.re_chat || false,
    stream: params.stream || true
  }

  console.log(`[maxKB API] sendMessage 调用参数:`, {
    chat_id: params.chat_id,
    requestData: requestData
  })

  return maxkbRequest({
    url: `/api/application/chat_message/${params.chat_id}`,
    method: 'post',
    data: requestData
  }).catch(error => {
    const fullUrl = `${MAXKB_CONFIG.baseURL}/api/application/chat_message/${params.chat_id}`
    console.error(`[maxKB API] sendMessage 请求失败:`)
    console.error(`[maxKB API] 接口地址: ${fullUrl}`)
    console.error(`[maxKB API] 请求数据:`, requestData)
    console.error(`[maxKB API] 错误详情:`, error)
    throw error
  })
}

/**
 * 流式发送消息到maxKB AI助手 (根据官方文档)
 * @param {Object} params
 * @param {string} params.message - 用户消息
 * @param {string} params.conversation_id - 会话ID（可选）
 * @param {Function} params.onMessage - 接收消息回调
 * @param {Function} params.onError - 错误回调
 * @param {Function} params.onComplete - 完成回调
 * @returns {Promise}
 */
export function sendStreamMessage(params) {
  return new Promise((resolve, reject) => {
    const eventSource = new EventSource(
      `${MAXKB_CONFIG.baseURL}/application/chat/stream?` +
      new URLSearchParams({
        message: params.message,
        conversation_id: params.conversation_id || '',
        authorization: `${MAXKB_CONFIG.apiKey}`
      })
    )

    eventSource.onmessage = function(event) {
      try {
        const data = JSON.parse(event.data)
        if (params.onMessage) {
          params.onMessage(data)
        }
      } catch (error) {
        console.error('解析流式数据失败:', error)
        if (params.onError) {
          params.onError(error)
        }
      }
    }

    eventSource.onerror = function(error) {
      console.error('流式连接错误:', error)
      eventSource.close()
      if (params.onError) {
        params.onError(error)
      }
      reject(error)
    }

    eventSource.addEventListener('close', function() {
      eventSource.close()
      if (params.onComplete) {
        params.onComplete()
      }
      resolve()
    })

    // 返回关闭函数
    return () => {
      eventSource.close()
    }
  })
}

/**
 * 创建新会话 (按照maxKB正确流程)
 * @returns {Promise}
 */
export async function createConversation() {
  try {
    console.log('[maxKB] 开始创建会话流程...')

    // 步骤1: 获取应用信息
    console.log('[maxKB] 步骤1: 调用 getApplicationProfile...')
    const appProfile = await getApplicationProfile()
    console.log('[maxKB] 步骤1 - 获取应用信息成功:', appProfile)

    if (!appProfile) {
      const error = new Error('getApplicationProfile 返回空数据')
      console.error('[maxKB] 步骤1 失败:', error.message)
      throw error
    }

    if (!appProfile.id) {
      const error = new Error('应用信息中缺少 id 字段')
      console.error('[maxKB] 步骤1 失败:', error.message)
      console.error('[maxKB] 应用信息结构:', appProfile)
      throw error
    }

    // 步骤2: 打开会话
    console.log(`[maxKB] 步骤2: 调用 openChat(${appProfile.id})...`)
    const chatResponse = await openChat(appProfile.id)
    console.log('[maxKB] 步骤2 - 打开会话响应:', chatResponse)

    if (!chatResponse) {
      const error = new Error('openChat 返回空数据')
      console.error('[maxKB] 步骤2 失败:', error.message)
      throw error
    }

    // 根据您提供的响应格式：{ "code": 200, "message": "成功", "data": "f2756af8-41dd-11f0-b546-0242ac120003" }
    // chat_id 在 data 字段中
    let chatId = null
    if (chatResponse.data) {
      chatId = chatResponse.data
    } else if (chatResponse.id) {
      // 兼容其他可能的响应格式
      chatId = chatResponse.id
    } else if (typeof chatResponse === 'string') {
      // 如果直接返回字符串
      chatId = chatResponse
    }

    if (!chatId) {
      const error = new Error('会话响应中缺少 chat_id')
      console.error('[maxKB] 步骤2 失败:', error.message)
      console.error('[maxKB] 会话响应结构:', chatResponse)
      throw error
    }

    const result = {
      id: chatId,
      applicationId: appProfile.id,
      applicationName: appProfile.name || 'maxKB AI助手'
    }

    console.log('[maxKB] 会话创建成功:', result)
    return result

  } catch (error) {
    console.error('[maxKB] 创建会话失败:')
    console.error('[maxKB] 错误类型:', error.constructor.name)
    console.error('[maxKB] 错误消息:', error.message)
    console.error('[maxKB] 错误堆栈:', error.stack)
    throw error
  }
}

/**
 * 步骤1: 获取应用配置信息 (根据maxKB官方文档) application-ce580c6f7987cb4b4abfcc35ccff0dad
 * 接口: GET /api/application/profile
 * @returns {Promise}
 */
export function getApplicationProfile() {
  return maxkbRequest({
    url: '/api/application/profile',
    method: 'get'
  })
}

/**
 * 获取应用信息 (兼容旧版本)
 * @returns {Promise}
 */
export function getApplicationInfo() {
  return getApplicationProfile()
}



// 导出配置，供其他模块使用
export { MAXKB_CONFIG }
