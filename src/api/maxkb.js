import request from '@/utils/request'

// maxKB AI助手API配置
const MAXKB_CONFIG = {
  // 这里需要配置您的maxKB服务地址和API密钥
  baseURL: process.env.VUE_APP_MAXKB_BASE_URL || 'http://localhost:8080',
  apiKey: process.env.VUE_APP_MAXKB_API_KEY || '',
  applicationId: process.env.VUE_APP_MAXKB_APPLICATION_ID || ''
}

/**
 * 发送消息到maxKB AI助手
 * @param {Object} params 
 * @param {string} params.message - 用户消息
 * @param {string} params.conversationId - 会话ID（可选）
 * @param {Array} params.history - 历史对话（可选）
 * @returns {Promise}
 */
export function sendMessage(params) {
  return request({
    url: `/api/application/${MAXKB_CONFIG.applicationId}/chat`,
    method: 'post',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`,
      'Content-Type': 'application/json'
    },
    data: {
      message: params.message,
      conversation_id: params.conversationId,
      history: params.history || [],
      stream: params.stream || false,
      ...params
    }
  })
}

/**
 * 流式发送消息到maxKB AI助手
 * @param {Object} params 
 * @param {string} params.message - 用户消息
 * @param {string} params.conversationId - 会话ID（可选）
 * @param {Function} params.onMessage - 接收消息回调
 * @param {Function} params.onError - 错误回调
 * @param {Function} params.onComplete - 完成回调
 * @returns {Promise}
 */
export function sendStreamMessage(params) {
  return new Promise((resolve, reject) => {
    const eventSource = new EventSource(
      `${MAXKB_CONFIG.baseURL}/api/application/${MAXKB_CONFIG.applicationId}/chat/stream?` +
      new URLSearchParams({
        message: params.message,
        conversation_id: params.conversationId || '',
        authorization: `Bearer ${MAXKB_CONFIG.apiKey}`
      })
    )

    eventSource.onmessage = function(event) {
      try {
        const data = JSON.parse(event.data)
        if (params.onMessage) {
          params.onMessage(data)
        }
      } catch (error) {
        console.error('解析流式数据失败:', error)
        if (params.onError) {
          params.onError(error)
        }
      }
    }

    eventSource.onerror = function(error) {
      console.error('流式连接错误:', error)
      eventSource.close()
      if (params.onError) {
        params.onError(error)
      }
      reject(error)
    }

    eventSource.addEventListener('close', function() {
      eventSource.close()
      if (params.onComplete) {
        params.onComplete()
      }
      resolve()
    })

    // 返回关闭函数
    return () => {
      eventSource.close()
    }
  })
}

/**
 * 获取会话历史
 * @param {string} conversationId - 会话ID
 * @returns {Promise}
 */
export function getConversationHistory(conversationId) {
  return request({
    url: `/api/application/${MAXKB_CONFIG.applicationId}/conversation/${conversationId}`,
    method: 'get',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`
    }
  })
}

/**
 * 创建新会话
 * @returns {Promise}
 */
export function createConversation() {
  return request({
    url: `/api/application/${MAXKB_CONFIG.applicationId}/conversation`,
    method: 'post',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`,
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 删除会话
 * @param {string} conversationId - 会话ID
 * @returns {Promise}
 */
export function deleteConversation(conversationId) {
  return request({
    url: `/api/application/${MAXKB_CONFIG.applicationId}/conversation/${conversationId}`,
    method: 'delete',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`
    }
  })
}

/**
 * 获取应用信息
 * @returns {Promise}
 */
export function getApplicationInfo() {
  return request({
    url: `/api/application/${MAXKB_CONFIG.applicationId}`,
    method: 'get',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`
    }
  })
}

/**
 * 获取会话列表
 * @returns {Promise}
 */
export function getConversationList() {
  return request({
    url: `/api/application/${MAXKB_CONFIG.applicationId}/conversation`,
    method: 'get',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`
    }
  })
}

// 导出配置，供其他模块使用
export { MAXKB_CONFIG }
