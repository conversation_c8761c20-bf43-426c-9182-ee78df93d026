import axios from 'axios'

// maxKB AI助手API配置
const MAXKB_CONFIG = {
  // 这里需要配置您的maxKB服务地址和API密钥
  baseURL: process.env.VUE_APP_MAXKB_BASE_URL || 'http://localhost:8080',
  apiKey: process.env.VUE_APP_MAXKB_API_KEY || '',
  applicationId: process.env.VUE_APP_MAXKB_APPLICATION_ID || ''
}

// 创建专用于maxKB的axios实例，避免被系统拦截器影响
const maxkbRequest = axios.create({
  baseURL: MAXKB_CONFIG.baseURL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// maxKB专用请求拦截器
maxkbRequest.interceptors.request.use(config => {
  // 为maxKB API添加专用的Authorization头
  if (MAXKB_CONFIG.apiKey) {
    config.headers['Authorization'] = `${MAXKB_CONFIG.apiKey}`
  }

  console.log(`[maxKB API] 请求: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`)
  console.log(`[maxKB API] Authorization:${MAXKB_CONFIG.apiKey}`)

  return config
}, error => {
  console.error('[maxKB API] 请求错误:', error)
  return Promise.reject(error)
})

// maxKB专用响应拦截器
maxkbRequest.interceptors.response.use(response => {
  console.log(`[maxKB API] 响应:`, response.data)
  return response.data
}, error => {
  console.error('[maxKB API] 响应错误:', error)

  if (error.response) {
    const { status, data } = error.response
    console.error(`[maxKB API] HTTP ${status}:`, data)

    if (status === 401) {
      throw new Error('maxKB API认证失败，请检查API Key')
    } else if (status === 404) {
      throw new Error('maxKB API端点不存在')
    } else if (status >= 500) {
      throw new Error('maxKB服务器错误')
    }
  } else if (error.request) {
    throw new Error('无法连接到maxKB服务')
  }

  return Promise.reject(error)
})

/**
 * 步骤2: 打开会话，获取会话ID (根据maxKB官方文档)
 * @param {string} applicationId - 应用ID
 * @returns {Promise}
 */
export function openChat(applicationId) {
  return maxkbRequest({
    url: `/api/application/${applicationId}/chat/open`,
    method: 'get'
  })
}

/**
 * 步骤3: 发送消息到maxKB AI助手 (根据官方文档)
 * @param {Object} params
 * @param {string} params.chat_id - 会话ID (必填)
 * @param {string} params.message - 用户消息
 * @param {boolean} params.stream - 是否流式输出
 * @returns {Promise}
 */
export function sendMessage(params) {
  if (!params.chat_id) {
    throw new Error('chat_id is required')
  }

  return maxkbRequest({
    url: `/api/application/chat_message/${params.chat_id}`,
    method: 'post',
    data: {
      message: params.message,
      stream: params.stream || false
    }
  })
}

/**
 * 流式发送消息到maxKB AI助手 (根据官方文档)
 * @param {Object} params
 * @param {string} params.message - 用户消息
 * @param {string} params.conversation_id - 会话ID（可选）
 * @param {Function} params.onMessage - 接收消息回调
 * @param {Function} params.onError - 错误回调
 * @param {Function} params.onComplete - 完成回调
 * @returns {Promise}
 */
export function sendStreamMessage(params) {
  return new Promise((resolve, reject) => {
    const eventSource = new EventSource(
      `${MAXKB_CONFIG.baseURL}/application/chat/stream?` +
      new URLSearchParams({
        message: params.message,
        conversation_id: params.conversation_id || '',
        authorization: `${MAXKB_CONFIG.apiKey}`
      })
    )

    eventSource.onmessage = function(event) {
      try {
        const data = JSON.parse(event.data)
        if (params.onMessage) {
          params.onMessage(data)
        }
      } catch (error) {
        console.error('解析流式数据失败:', error)
        if (params.onError) {
          params.onError(error)
        }
      }
    }

    eventSource.onerror = function(error) {
      console.error('流式连接错误:', error)
      eventSource.close()
      if (params.onError) {
        params.onError(error)
      }
      reject(error)
    }

    eventSource.addEventListener('close', function() {
      eventSource.close()
      if (params.onComplete) {
        params.onComplete()
      }
      resolve()
    })

    // 返回关闭函数
    return () => {
      eventSource.close()
    }
  })
}

/**
 * 创建新会话 (按照maxKB正确流程)
 * @returns {Promise}
 */
export async function createConversation() {
  try {
    // 步骤1: 获取应用信息
    const appProfile = await getApplicationProfile()
    console.log('[maxKB] 步骤1 - 获取应用信息:', appProfile)

    if (!appProfile || !appProfile.id) {
      throw new Error('无法获取应用ID')
    }

    // 步骤2: 打开会话
    const chatSession = await openChat(appProfile.id)
    console.log('[maxKB] 步骤2 - 打开会话:', chatSession)

    if (!chatSession || !chatSession.id) {
      throw new Error('无法获取会话ID')
    }

    return {
      id: chatSession.id,
      applicationId: appProfile.id,
      applicationName: appProfile.name || 'maxKB AI助手'
    }
  } catch (error) {
    console.error('[maxKB] 创建会话失败:', error)
    throw error
  }
}

/**
 * 获取应用配置信息 (根据maxKB官方文档)
 * @returns {Promise}
 */
export function getApplicationProfile() {
  return maxkbRequest({
    url: '/api/application/profile',
    method: 'get'
  })
}

/**
 * 获取应用信息 (兼容旧版本)
 * @returns {Promise}
 */
export function getApplicationInfo() {
  return getApplicationProfile()
}



// 导出配置，供其他模块使用
export { MAXKB_CONFIG }
