import request from '@/utils/request'

// maxKB AI助手API配置
const MAXKB_CONFIG = {
  // 这里需要配置您的maxKB服务地址和API密钥
  baseURL: process.env.VUE_APP_MAXKB_BASE_URL || 'http://localhost:8080',
  apiKey: process.env.VUE_APP_MAXKB_API_KEY || '',
  applicationId: process.env.VUE_APP_MAXKB_APPLICATION_ID || ''
}

/**
 * 发送消息到maxKB AI助手 (根据官方文档)
 * @param {Object} params
 * @param {string} params.message - 用户消息
 * @param {string} params.conversation_id - 会话ID（可选）
 * @param {boolean} params.stream - 是否流式输出
 * @returns {Promise}
 */
export function sendMessage(params) {
  return request({
    url: '/application/chat',
    method: 'post',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `${MAXKB_CONFIG.apiKey}`,
      'Content-Type': 'application/json'
    },
    data: {
      message: params.message,
      conversation_id: params.conversation_id || params.conversationId,
      stream: params.stream || false
    }
  })
}

/**
 * 流式发送消息到maxKB AI助手 (根据官方文档)
 * @param {Object} params
 * @param {string} params.message - 用户消息
 * @param {string} params.conversation_id - 会话ID（可选）
 * @param {Function} params.onMessage - 接收消息回调
 * @param {Function} params.onError - 错误回调
 * @param {Function} params.onComplete - 完成回调
 * @returns {Promise}
 */
export function sendStreamMessage(params) {
  return new Promise((resolve, reject) => {
    const eventSource = new EventSource(
      `${MAXKB_CONFIG.baseURL}/application/chat/stream?` +
      new URLSearchParams({
        message: params.message,
        conversation_id: params.conversation_id || '',
        authorization: `Bearer ${MAXKB_CONFIG.apiKey}`
      })
    )

    eventSource.onmessage = function(event) {
      try {
        const data = JSON.parse(event.data)
        if (params.onMessage) {
          params.onMessage(data)
        }
      } catch (error) {
        console.error('解析流式数据失败:', error)
        if (params.onError) {
          params.onError(error)
        }
      }
    }

    eventSource.onerror = function(error) {
      console.error('流式连接错误:', error)
      eventSource.close()
      if (params.onError) {
        params.onError(error)
      }
      reject(error)
    }

    eventSource.addEventListener('close', function() {
      eventSource.close()
      if (params.onComplete) {
        params.onComplete()
      }
      resolve()
    })

    // 返回关闭函数
    return () => {
      eventSource.close()
    }
  })
}

/**
 * 创建新会话 (简化版本，根据maxKB文档，会话ID可以自动生成)
 * @returns {Promise}
 */
export function createConversation() {
  // 根据maxKB文档，可以直接生成UUID作为会话ID
  return Promise.resolve({
    id: 'conv_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
  })
}

/**
 * 获取应用配置信息 (根据maxKB官方文档)
 * @returns {Promise}
 */
export function getApplicationProfile() {
  return request({
    url: '/application/profile',
    method: 'get',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `${MAXKB_CONFIG.apiKey}`
    }
  })
}

/**
 * 获取应用信息 (兼容旧版本)
 * @returns {Promise}
 */
export function getApplicationInfo() {
  return getApplicationProfile()
}



// 导出配置，供其他模块使用
export { MAXKB_CONFIG }
