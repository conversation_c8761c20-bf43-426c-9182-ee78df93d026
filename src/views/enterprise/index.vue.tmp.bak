<template>
  <div class="app-container enterprise-archive">
    <div class="enterprise-header">
      <div class="header-left">

        <div class="search-container">
          <div class="complex-search">
            <el-select v-model="searchType" class="search-type">
              <el-option label="全部单位" value="all"></el-option>
              <el-option label="单位名称" value="name"></el-option>
              <el-option label="统一社会信用编码" value="creditCode"></el-option>
              <el-option label="法人" value="legalPerson"></el-option>
            </el-select>
            <el-input
              v-model="searchKeyword"
              placeholder="请输入关键词"
              clearable
              @keyup.enter.native="handleSearch"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </div>
        </div>
      </div>
      <div class="header-right">
        <!-- 按钮已移至列表头部 -->
      </div>
    </div>

    <div class="broad-list" :class="{ 'is-loading': isLoading }">
      <broad-list
        :items="categoryItems"
        :horizontal="true"
        class="horizontal"
        :selectedIndex="activeCategory"
        @item-click="handleCategoryClick"
      ></broad-list>
      <div class="loading-overlay" v-if="isLoading">
        <i class="el-icon-loading"></i>
        <span>加载中...</span>
      </div>
    </div>

    <div class="enterprise-content">
      <!-- <div class="filter-container">
        <div class="filter-item">
          <span class="label">行业分类：</span>
          <el-select v-model="filters.industry" placeholder="全部" clearable>
            <el-option v-for="item in industryOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <span class="label">企业规模：</span>
          <el-select v-model="filters.scale" placeholder="全部" clearable>
            <el-option v-for="item in scaleOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <span class="label">风险等级：</span>
          <el-select v-model="filters.riskLevel" placeholder="全部" clearable>
            <el-option v-for="item in riskLevelOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <span class="label">所属区域：</span>
          <el-select v-model="filters.region" placeholder="全部" clearable>
            <el-option v-for="item in regionOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <el-button type="primary" size="small" @click="handleFilter">筛选</el-button>
          <el-button size="small" @click="resetFilter">重置</el-button>
        </div>
      </div> -->

      <div class="enterprise-list">
          <div class="list-header">
            <div class="left-section">
              <!-- 暂时隐藏全选按钮 -->
              <!-- <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox> -->

              <!-- 添加操作按钮 -->
              <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAddEnterprise">新增企业</el-button>
              <el-button type="success" size="small" icon="el-icon-upload2" @click="handleImport">批量导入</el-button>
              <el-button type="info" size="small" icon="el-icon-download" @click="handleExport">导出</el-button>
              <el-button type="danger" size="small" icon="el-icon-delete" @click="handleBatchDelete">批量删除</el-button>
            </div>
            <div class="right-section">
              <div class="batch-actions" v-if="selectedItems.length > 0">
                <span class="selected-count">已选择 {{ selectedItems.length }} 项</span>
              </div>
              <!-- 添加设置和刷新按钮 -->
              <el-button
                type="text"
                icon="el-icon-setting"
                @click="handleDisplayFieldsSettings"
                class="action-button"
              >
                <span class="action-text">设置显示字段</span>
              </el-button>
              <el-button
                type="text"
                icon="el-icon-refresh"
                @click="handleRefreshList"
                class="action-button"
              >
                <span class="action-text">刷新列表</span>
              </el-button>
            </div>
          </div>
          <div class="enterprise-row"
            v-for="(item, index) in enterpriseList"
            :key="index"
            @click="handleRowClick(item)"
            :class="{'selected': item.selected}"
          >
            <!-- 暂时隐藏复选框 -->
            <div class="item-checkbox" @click.stop>
                <el-checkbox v-model="item.selected" @change="(val) => handleItemSelect(val, item)"></el-checkbox>
            </div>
            <div class="enterprise-list-item">
                <el-card class="enterprise-card" shadow="hover">
                    <div class="card-body">
                        <div class="card-left">
                            <div class="card-header">
                                <span class="enterprise-name">{{ item.name }}</span>
                                <el-tag
                                  v-for="(level, levelIndex) in item.riskLevels"
                                  :key="levelIndex"
                                  plain
                                  size="mini"
                                  :style="{ color: getRiskLevelColor(level), borderColor: getRiskLevelColor(level), backgroundColor: getLightenedColor(level) }"
                                  class="risk-tag"
                                >
                                  {{ getRiskLevelLabel(level) }} {{ level }}
                                </el-tag>
                            </div>
                            <div class="card-content">
                                <div class="info-item" v-if="displayFields.creditCode">
                                    <span class="label">{{ item.creditCodeLabel }}</span>
                                    <span class="value">{{ item.creditCode }}</span>
                                </div>
                                <div class="info-item" v-if="displayFields.legalPerson">
                                    <span class="label">{{ item.legalPersonLabel }}</span>
                                    <span class="value">{{ item.legalPerson }} ({{ formatPhone(item.contactPhone) }})</span>
                                </div>
                                <div class="info-item" v-if="displayFields.companyType">
                                    <span class="label">{{ item.companyTypeLabel }}</span>
                                    <span class="value">{{ item.companyType }}</span>
                                </div>
                                <div class="info-item" v-if="displayFields.manager">
                                    <span class="label">{{ item.managerLabel }}</span>
                                    <span class="value">{{ item.manager }} ({{ formatPhone(item.managerPhone) }})</span>
                                </div>
                                <div class="info-item" v-if="displayFields.status">
                                    <span class="label">{{ item.statusLabel }}</span>
                                    <span class="value" :style="{ color: getStateColor(item.state) }">{{ item.status }}</span>
                                </div>
                                <div class="info-item" v-if="displayFields.relatedOthers">
                                    <span class="label">{{ item.relatedOthersLabel }}</span>
                                    <span class="value">{{ item.relatedOthers }}</span>
                                </div>
                                <div class="info-item full-width" v-if="displayFields.address">
                                    <span class="label">{{ item.addressLabel }}</span>
                                    <span class="value">{{ item.address }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <el-button type="text" icon="el-icon-edit" @click.stop="handleEdit(item)">编辑</el-button>
                            <el-button type="text" icon="el-icon-delete" @click.stop="handleDelete(item)">删除</el-button>
                            <el-button type="text" icon="el-icon-view" @click.stop="handleView(item)">查看详情</el-button>
                        </div>
                    </div>
                </el-card>
            </div>
          </div>
      </div>

      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[12, 24, 36, 48]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </div>

    <!-- 企业详情对话框 -->
    <el-dialog title="企业详情" :visible.sync="detailDialogVisible" width="70%">
      <enterprise-detail v-if="detailDialogVisible" :enterprise-id="currentEnterpriseId"></enterprise-detail>
    </el-dialog>

    <!-- 新增/编辑企业对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="formDialogVisible" width="50%">
      <enterprise-form
        v-if="formDialogVisible"
        :enterprise="currentEnterprise"
        @submit="handleFormSubmit"
        @cancel="formDialogVisible = false">
      </enterprise-form>
    </el-dialog>

    <!-- 显示字段设置对话框 -->
    <el-dialog title="设置显示字段" :visible.sync="displayFieldsDialogVisible" width="30%">
      <div class="display-fields-container">
        <el-checkbox v-model="displayFields.creditCode">统一社会信用编码</el-checkbox>
        <el-checkbox v-model="displayFields.legalPerson">法人及联系电话</el-checkbox>
        <el-checkbox v-model="displayFields.companyType">公司性质</el-checkbox>
        <el-checkbox v-model="displayFields.manager">负责人及联系电话</el-checkbox>
        <el-checkbox v-model="displayFields.status">状态</el-checkbox>
        <el-checkbox v-model="displayFields.relatedOthers">关联其他</el-checkbox>
        <el-checkbox v-model="displayFields.address">单位注册地址</el-checkbox>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="displayFieldsDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveDisplayFieldsSettings">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EnterpriseDetail from './components/EnterpriseDetail'
import EnterpriseForm from './components/EnterpriseForm'
import BroadList from '@/components/broad-list.vue'
import { getEnterpriseList, getEnterpriseCategoryStats, getRiskLevelOptions, addEnterprise, updateEnterprise, deleteEnterprise, batchDeleteEnterprise } from '@/api/enterprise'

import EnterpriseForm from './components/EnterpriseForm'
import BroadList from '@/components/broad-list.vue'
import { getEnterpriseList, getEnterpriseCategoryStats, getRiskLevelOptions, addEnterprise, updateEnterprise, deleteEnterprise, batchDeleteEnterprise } from '@/api/enterprise'

export default {
import BroadList from '@/components/broad-list.vue'
import { getEnterpriseList, getEnterpriseCategoryStats, getRiskLevelOptions, addEnterprise, updateEnterprise, deleteEnterprise, batchDeleteEnterprise } from '@/api/enterprise'

export default {
  name: 'EnterpriseArchive',
  components: {
    EnterpriseDetail,
    EnterpriseForm,
    BroadList
  },
  data() {
    return {
      searchKeyword: '',
      searchType: 'all',
      activeCategory: 0, // 默认选中第一个分类（全部单位）
      currentCategoryType: 'all', // 当前选中的分类类型
      isLoading: false, // 加载状态
      categoryItems: [
        {
          title: '全部单位',
          badge: '1564',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#2A52D7'
        },
        {
          title: '关联电子屏',
          badge: '564',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#60B8FF'
        },
        {
          title: '关联等保备案',
          badge: '364',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#44C991'
        },
        {
          title: '关联网站备案',
          badge: '264',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#F5BC6C'
        },
        {
          title: '关联运营商',
          badge: '464',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#24A8BB'
        },
        {
          title: '关联网吧',
          badge: '264',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#FB6B2A'
        },
        {
          title: '关联非经营',
          badge: '44',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#D789D4'
        },
        {
          title: '关联其他',
          badge: '',
          backgroundColor: '#F5F7FA',
          backgroundColor_active: '#95ABD4'
        }
      ],
      filters: {
        industry: '',
        scale: '',
        riskLevel: '',
        region: ''
      },
      industryOptions: [
        { value: '制造业', label: '制造业' },
        { value: '服务业', label: '服务业' },
        { value: '建筑业', label: '建筑业' },
        { value: '金融业', label: '金融业' },
        { value: '其他', label: '其他' }
      ],
      scaleOptions: [
        { value: '大型', label: '大型' },
        { value: '中型', label: '中型' },
        { value: '小型', label: '小型' },
        { value: '微型', label: '微型' }
      ],



      riskLevelOptions: [
        { value: '1', label: '关联网吧', tagColor: '#FB6B2A' },
        { value: '2', label: '关联电子屏', tagColor: '#60B8FF' },
        { value: '3', label: '关联等保备案', tagColor: '#44C991' },
        { value: '4', label: '关联网站备案', tagColor: '#F5BC6C' },
        { value: '5', label: '关联运营商', tagColor: '#24A8BB' },
        { value: '6', label: '关联非经营', tagColor: '#D789D4' },
        { value: '7', label: '关联其他', tagColor: '#95ABD4' }
      ],
      regionOptions: [
        { value: '通州区', label: '通州区' },
        { value: '朝阳区', label: '朝阳区' },
        { value: '海淀区', label: '海淀区' },
        { value: '丰台区', label: '丰台区' },
        { value: '其他', label: '其他' }
      ],
      enterpriseList: [],
      pagination: {
        currentPage: 1,
        pageSize: 12,
        total: 0
      },
      detailDialogVisible: false,
      formDialogVisible: false,
      currentEnterpriseId: null,
      currentEnterprise: null,
      dialogTitle: '新增企业',
      selectedItems: [], // 存储选中的企业ID
      selectAll: false, // 是否全选

      // 显示字段设置
      displayFieldsDialogVisible: false,
      displayFields: {
        creditCode: true,
        legalPerson: true,
        companyType: true,
        manager: true,
        status: true,
        relatedOthers: true,
        address: true
      }
    }
  },
  created() {
    // 从本地存储加载显示字段设置
    const savedDisplayFields = localStorage.getItem('enterpriseDisplayFields');
    if (savedDisplayFields) {
      try {
        this.displayFields = JSON.parse(savedDisplayFields);
      } catch (e) {
        console.error('解析显示字段设置失败', e);
      }
    }

    // 确保默认选中"全部单位"标签
    this.activeCategory = 0;
    this.currentCategoryType = 'all';

    // 初始化时调用API获取数据 - 明确使用 'all' 类型
    this.fetchEnterpriseListByApi('all');
  },
  methods: {
    // 获取企业列表
    fetchEnterpriseList() {
      try {
        // 确保使用 'all' 作为默认值
        const categoryType = this.currentCategoryType || 'all';
        console.log('fetchEnterpriseList - categoryType:', categoryType);

        // 构建请求参数
        const params = {
          categoryType: categoryType,
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          keyword: this.searchKeyword,
          searchType: this.searchType,
          ...this.filters
        };

        // 调用API获取企业列表数据
        getEnterpriseList(params).then(response => {
          if (response.code === 200 && response.data) {
            const { total, list } = response.data;

            // 更新分页总数
            this.pagination.total = total;

            // 处理企业列表数据
            this.enterpriseList = list.map(item => {
              // 添加 selected 属性，用于复选框
              return {
                ...item,
                selected: this.selectedItems.includes(item.id)
              };
            });

            // 重置全选状态
            this.selectAll = this.enterpriseList.length > 0 && this.selectedItems.length === this.enterpriseList.length;
          } else {
            console.warn('获取到的数据为空或格式不正确');
            this.enterpriseList = [];
            this.pagination.total = 0;
          }
        }).catch(error => {
          console.error('获取企业列表失败:', error);
          this.$message.error('获取企业列表失败，请稍后重试');
          this.enterpriseList = [];
          this.pagination.total = 0;
        });
      } catch (error) {
        console.error('获取企业列表失败:', error);
        this.$message.error('获取企业列表失败，请稍后重试');
        this.enterpriseList = [];
        this.pagination.total = 0;
      }
    },

    // 筛选数据
    filterData(data) {
      const { industry, scale, riskLevel, region } = this.filters
      const keyword = this.searchKeyword.toLowerCase()
      const searchType = this.searchType
      const categoryType = this.currentCategoryType

      return data.filter(item => {
        let matchKeyword = true

        if (keyword) {
          switch (searchType) {
            case 'name':
              matchKeyword = item.name.toLowerCase().includes(keyword)
              break
            case 'creditCode':
              matchKeyword = item.creditCode.toLowerCase().includes(keyword)
              break
            case 'legalPerson':
              matchKeyword = (item.legalPerson || '').toLowerCase().includes(keyword)
              break
            case 'all':
            default:
              matchKeyword = item.name.toLowerCase().includes(keyword) ||
                item.creditCode.toLowerCase().includes(keyword) ||
                (item.legalPerson || '').toLowerCase().includes(keyword)
              break
          }
        }

        // 根据分类类型筛选
        let matchCategory = true
        if (categoryType !== 'all') {
          // 这里根据实际业务逻辑判断企业是否属于特定分类
          // 示例：假设企业数据中有 categories 字段表示所属分类
          switch(categoryType) {
            case 'screen':
              matchCategory = item.hasElectronicScreen === true
              break
            case 'security':
              matchCategory = item.hasSecurityRecord === true
              break
            case 'website':
              matchCategory = item.hasWebsiteRecord === true
              break
            case 'operator':
              matchCategory = item.hasOperator === true
              break
            case 'netbar':
              matchCategory = item.hasNetbar === true
              break
            case 'nonbusiness':
              matchCategory = item.isNonBusiness === true
              break
            case 'other':
              // 不属于上述任何分类的企业
              matchCategory = !item.hasElectronicScreen &&
                             !item.hasSecurityRecord &&
                             !item.hasWebsiteRecord &&
                             !item.hasOperator &&
                             !item.hasNetbar &&
                             !item.isNonBusiness
              break
          }
        }

        const matchIndustry = !industry || item.industry === industry
        const matchScale = !scale || item.scale === scale
        const matchRiskLevel = !riskLevel || (item.riskLevels && item.riskLevels.includes(riskLevel))
        const matchRegion = !region || item.region === region

        return matchKeyword && matchIndustry && matchScale && matchRiskLevel && matchRegion && matchCategory
      })
    },

    // 根据风险等级获取标签类型
    getRiskLevelType(level) {
      // 查找对应的风险等级选项
      const option = this.riskLevelOptions.find(opt => opt.value === level);
      if (option) {
        return ''; // 返回空字符串，我们将使用自定义颜色
      }
      return 'info'; // 默认颜色
    },

    // 获取标签颜色
    getRiskLevelColor(level) {
      const option = this.riskLevelOptions.find(opt => opt.value === level);
      if (option) {
        return option.tagColor;
      }
      return '#909399'; // 默认颜色
    },

    // 获取标签文本
    getRiskLevelLabel(level) {
      const option = this.riskLevelOptions.find(opt => opt.value === level);
      if (option) {
        return option.label;
      }
      return '未知'; // 默认返回未知
    },

    // 处理搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.fetchEnterpriseList()
    },

    // 处理筛选
    handleFilter() {
      this.pagination.currentPage = 1
      this.fetchEnterpriseList()
    },

    // 重置筛选条件
    resetFilter() {
      this.filters = {
        industry: '',
        scale: '',
        riskLevel: '',
        region: ''
      }
      this.fetchEnterpriseList()
    },

    // 处理页码变化
    handleCurrentChange(currentPage) {
      this.pagination.currentPage = currentPage
      this.fetchEnterpriseList()
    },

    // 处理每页显示数量变化
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize
      this.fetchEnterpriseList()
    },

    // 处理行点击
    handleRowClick(item) {
      // 切换选中状态
      const selected = !item.selected;
      this.$set(item, 'selected', selected);
      this.handleItemSelect(selected, item);
    },

    // 处理查看详情
    handleView(item) {
      // 跳转到详情报告页面
      this.$router.push(`/enterprise/detail/${item.id}`)
    },


    // 处理编辑
    handleEdit(item) {
      this.dialogTitle = '编辑企业'
      this.currentEnterprise = { ...item }
      this.formDialogVisible = true
    },

    // 处理删除
    handleDelete(item) {
      this.$confirm('确认删除该企业档案吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用API删除企业
        deleteEnterprise(item.id).then(response => {
          if (response.code === 200) {
            // 从列表中移除已删除的企业
            this.enterpriseList = this.enterpriseList.filter(enterprise => enterprise.id !== item.id);
            this.$message.success('删除成功');

            // 更新分类统计数据
            this.fetchCategoryStats();
          } else {
            this.$message.error(response.msg || '删除失败');
          }
        }).catch(error => {
          console.error('删除企业失败:', error);
          this.$message.error('删除失败，请稍后重试');
        });
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 处理新增企业
    handleAddEnterprise() {
      this.dialogTitle = '新增企业'
      this.currentEnterprise = null
      this.formDialogVisible = true
    },

    // 处理表单提交
    handleFormSubmit(formData) {
      if (formData.id) {
        // 编辑现有企业
        updateEnterprise(formData).then(response => {
          if (response.code === 200) {
            // 更新列表中的企业数据
            const index = this.enterpriseList.findIndex(item => item.id === formData.id);
            if (index !== -1) {
              this.enterpriseList.splice(index, 1, formData);
            }
            this.$message.success('更新成功');

            // 更新分类统计数据
            this.fetchCategoryStats();
          } else {
            this.$message.error(response.msg || '更新失败');
          }
        }).catch(error => {
          console.error('更新企业失败:', error);
          this.$message.error('更新失败，请稍后重试');
        });
      } else {
        // 新增企业
        addEnterprise(formData).then(response => {
          if (response.code === 200) {
            // 添加新企业到列表
            const newEnterprise = {
              id: Date.now(), // 实际应该使用后端返回的ID
              ...formData
            };
            this.enterpriseList.unshift(newEnterprise);
            this.$message.success('添加成功');

            // 更新分类统计数据
            this.fetchCategoryStats();
          } else {
            this.$message.error(response.msg || '添加失败');
          }
        }).catch(error => {
          console.error('添加企业失败:', error);
          this.$message.error('添加失败，请稍后重试');
        });
      }
      this.formDialogVisible = false;
    },

    // 处理导入
    handleImport() {
      this.$message.info('批量导入功能开发中...')
    },

    // 处理导出
    handleExport() {
      this.$message.info('导出功能开发中...')
    },

    // 格式化电话号码，中间4位用星号代替
    formatPhone(phone) {
      if (!phone) return '';
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },

    // 获取状态颜色
    getStateColor(state) {
      if (state === 'valid') {
        return '#4584FF'; // 有效状态为蓝色
      } else if (state === 'invalid') {
        return '#F56C6C'; // 无效状态为红色
      }
      return '#909399'; // 默认颜色
    },

    // 获取变浅的标签背景色
    getLightenedColor(level) {
      const option = this.riskLevelOptions.find(opt => opt.value === level);
      if (option) {
        // 直接在这里实现 lightenColor 方法的功能
        const hexColor = option.tagColor.replace(/^#/, "");
        const r = parseInt(hexColor.substring(0, 2), 16);
        const g = parseInt(hexColor.substring(2, 4), 16);
        const b = parseInt(hexColor.substring(4, 6), 16);

        // 计算变浅后的RGB值（90%变浅）
        const percent = 90;
        const newR = Math.round(r + (255 - r) * (percent / 100));
        const newG = Math.round(g + (255 - g) * (percent / 100));
        const newB = Math.round(b + (255 - b) * (percent / 100));

        // 将RGB转换回16进制
        const toHex = (value) => {
          const hex = value.toString(16);
          return hex.length === 1 ? "0" + hex : hex;
        };

        return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
      }
      return "#F5F7FA"; // 默认背景色
    },


    // 生成随机的风险级别标签
    generateRandomRiskLevels() {
      const levels = [];
      const count = Math.floor(Math.random() * 7) + 1; // 1-7个标签

      // 创建一个包含所有可能值的数组
      const allValues = this.riskLevelOptions.map(option => option.value);

      // 随机选择count个不重复的值
      for (let i = 0; i < count; i++) {
        if (allValues.length === 0) break;

        const randomIndex = Math.floor(Math.random() * allValues.length);
        levels.push(allValues[randomIndex]);
        allValues.splice(randomIndex, 1); // 移除已选择的值，确保不重复
      }

      return levels;
    },

    // 调用接口获取企业列表数据
    fetchEnterpriseListByApi(categoryType = 'all') {
      // 确保使用有效的分类类型
      categoryType = categoryType || 'all';

      // 设置加载状态
      this.isLoading = true;

      // 显示加载状态
      this.$message({
        message: `正在加载${this.getCategoryTitle(categoryType)}数据...`,
        type: 'info',
        duration: 1000
      });

      // 更新当前选中的分类类型
      this.currentCategoryType = categoryType;

      // 构建请求参数
      const params = {
        categoryType: categoryType,
        pageNum: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        keyword: this.searchKeyword,
        searchType: this.searchType,
        ...this.filters
      };

      // 调用API获取企业列表数据
      getEnterpriseList(params).then(response => {
        if (response.code === 200 && response.data) {
          const { total, list } = response.data;

          // 更新分页总数
          this.pagination.total = total;

          // 处理企业列表数据
          this.enterpriseList = list.map(item => {
            // 添加 selected 属性，用于复选框
            return {
              ...item,
              selected: this.selectedItems.includes(item.id)
            };
          });

          // 重置全选状态
          this.selectAll = this.enterpriseList.length > 0 && this.selectedItems.length === this.enterpriseList.length;

          // 显示加载完成状态
          this.$message({
            message: `${this.getCategoryTitle(categoryType)}数据加载完成`,
            type: 'success',
            duration: 1500
          });
        } else {
          console.warn('获取到的数据为空或格式不正确');
          this.enterpriseList = [];
          this.pagination.total = 0;
          this.$message.error('获取数据失败，请稍后重试');
        }
      }).catch(error => {
        console.error('获取企业列表失败:', error);
        this.$message.error('获取数据失败，请稍后重试');
        this.enterpriseList = [];
        this.pagination.total = 0;
      }).finally(() => {
        // 确保在任何情况下都清除加载状态
        this.isLoading = false;
      });
    },

    // 根据分类类型获取分类标题
    getCategoryTitle(categoryType) {
      switch(categoryType) {
        case 'all': return '全部单位';
        case 'screen': return '关联电子屏';
        case 'security': return '关联等保备案';
        case 'website': return '关联网站备案';
        case 'operator': return '关联运营商';
        case 'netbar': return '关联网吧';
        case 'nonbusiness': return '关联非经营';
        case 'other': return '其他';
        default: return '';
      }
    },

    // 处理分类点击
    handleCategoryClick(item, index) {
      // 更新选中的分类索引
      this.activeCategory = index;

      // 根据分类类型设置筛选条件
      let filterType = '';
      switch(item.title) {
        case '全部单位':
          // 不设置特定筛选条件，显示所有单位
          filterType = 'all';
          break;
        case '关联电子屏':
          filterType = 'screen';
          break;
        case '关联等保备案':
          filterType = 'security';
          break;
        case '关联网站备案':
          filterType = 'website';
          break;
        case '关联运营商':
          filterType = 'operator';
          break;
        case '关联网吧':
          filterType = 'netbar';
          break;
        case '关联非经营':
          filterType = 'nonbusiness';
          break;
        case '其他':
          filterType = 'other';
          break;
      }

      console.log('handleCategoryClick - 切换到分类:', filterType);

      // 设置当前选中的分类类型
      this.currentCategoryType = filterType;

      // 重置分页
      this.pagination.currentPage = 1;

      // 调用接口获取数据
      this.fetchEnterpriseListByApi(filterType);
    },

    // 处理选择单个项目
    handleItemSelect(selected, item) {
      if (selected) {
        // 添加到选中列表
        this.selectedItems.push(item.id);
      } else {
        // 从选中列表中移除
        const index = this.selectedItems.indexOf(item.id);
        if (index !== -1) {
          this.selectedItems.splice(index, 1);
        }
      }

      // 检查是否所有项目都被选中
      this.selectAll = this.enterpriseList.length > 0 && this.selectedItems.length === this.enterpriseList.length;
    },

    // 处理全选/取消全选
    handleSelectAll(val) {
      if (val) {
        // 全选
        this.selectedItems = this.enterpriseList.map(item => item.id);
        this.enterpriseList.forEach(item => {
          this.$set(item, 'selected', true);
        });
      } else {
        // 取消全选
        this.selectedItems = [];
        this.enterpriseList.forEach(item => {
          this.$set(item, 'selected', false);
        });
      }
    },

    // 处理设置显示字段
    handleDisplayFieldsSettings() {
      this.displayFieldsDialogVisible = true;
    },

    // 保存显示字段设置
    saveDisplayFieldsSettings() {
      this.displayFieldsDialogVisible = false;
      // 可以在这里保存设置到本地存储或后端
      localStorage.setItem('enterpriseDisplayFields', JSON.stringify(this.displayFields));
      this.$message.success('显示字段设置已保存');
    },

    // 处理刷新列表
    handleRefreshList() {
      // 使用当前选中的分类类型刷新数据，确保使用 'all' 作为默认值
      const categoryType = this.currentCategoryType || 'all';
      this.fetchEnterpriseListByApi(categoryType);
    },

    // 获取风险级别选项
    fetchRiskLevelOptions() {
      getRiskLevelOptions().then(response => {
        if (response.code === 200 && response.data) {
          this.riskLevelOptions = response.data;
        } else {
          console.warn('获取风险级别选项失败');
        }
      }).catch(error => {
        console.error('获取风险级别选项失败:', error);
      });
    },

    // 获取分类统计数据
    fetchCategoryStats() {
      getEnterpriseCategoryStats().then(response => {
        if (response.code === 200 && response.data) {
          // 更新分类数据
          const categoryStats = response.data;

          // 更新分类项的数量标签
          this.categoryItems = this.categoryItems.map((item, index) => {
            const stat = categoryStats[index];
            if (stat) {
              return {
                ...item,
                badge: stat.count.toString(),
                backgroundColor: item.backgroundColor,
                backgroundColor_active: stat.color || item.backgroundColor_active
              };
            }
            return item;
          });
        } else {
          console.warn('获取分类统计数据失败');
        }
      }).catch(error => {
        console.error('获取分类统计数据失败:', error);
      });
    },

    // 处理批量删除
    handleBatchDelete() {
      // 如果没有选中项，提示用户但不阻止操作
      const confirmMessage = this.selectedItems.length > 0
        ? `确认删除选中的 ${this.selectedItems.length} 项企业档案吗？`
        : '确认批量删除所有企业档案吗？';

      this.$confirm(confirmMessage, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用API批量删除企业
        const ids = this.selectedItems.length > 0 ? this.selectedItems : null;

        batchDeleteEnterprise(ids).then(response => {
          if (response.code === 200) {
            if (this.selectedItems.length > 0) {
              // 删除选中的企业
              this.enterpriseList = this.enterpriseList.filter(item => !this.selectedItems.includes(item.id));
              this.$message.success(`已删除 ${this.selectedItems.length} 项企业档案`);
            } else {
              // 删除所有企业
              const totalCount = this.enterpriseList.length;
              this.enterpriseList = [];
              this.$message.success(`已删除全部 ${totalCount} 项企业档案`);
            }

            // 清空选中状态
            this.selectedItems = [];
            this.selectAll = false;

            // 更新分类统计数据
            this.fetchCategoryStats();
          } else {
            this.$message.error(response.msg || '批量删除失败');
          }
        }).catch(error => {
          console.error('批量删除企业失败:', error);
          this.$message.error('批量删除失败，请稍后重试');
        });
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.enterprise-archive {
  .enterprise-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;

      h2 {
        margin: 0 20px 0 0;
        font-size: 20px;
        font-weight: 600;
      }

      .search-container {
        width: 450px;

        .complex-search {
          display: flex;

          .search-type {
            width: 150px;
            margin-right: -1px;

            ::v-deep .el-input__inner {
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
            }
          }

          .el-input {
            flex: 1;

            ::v-deep .el-input__inner {
              border-top-left-radius: 0;
              border-bottom-left-radius: 0;
            }
          }
        }
      }
    }

    .header-right {
      display: flex;
      gap: 10px;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;

      .header-left {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 15px;

        h2 {
          margin-bottom: 15px;
        }

        .search-container {
          width: 100%;

          .complex-search {
            flex-direction: column;

            .search-type {
              width: 100%;
              margin-right: 0;
              margin-bottom: 10px;

              ::v-deep .el-input__inner {
                border-radius: 4px;
              }
            }

            .el-input {
              ::v-deep .el-input__inner {
                border-radius: 4px;
              }
            }
          }
        }
      }

      .header-right {
        justify-content: flex-end;
      }
    }
  }

  .broad-list {
    margin-bottom: 20px;
    padding: 0 10px;
    position: relative;

    &.is-loading {
      pointer-events: none;
      opacity: 0.7;
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.7);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 10;

      i {
        font-size: 32px;
        color: #409EFF;
        margin-bottom: 10px;
      }

      span {
        font-size: 14px;
        color: #606266;
      }
    }

    ::v-deep .broad-list-container {
      .broad-list {
        padding: 10px 0;

        .broad-list-item {
          height: 100px;
          display: flex;
          align-items: center;
          justify-content: center;

          .item-content {
            .item-badge {
              font-size: 28px;
              font-weight: bold;
              margin-bottom: 10px;
               &.active {
                    color: #ffffff;
                }
            }

            .item-title {
              font-weight: 500;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .enterprise-content {
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;

      .filter-item {
        display: flex;
        align-items: center;

        .label {
          margin-right: 8px;
          white-space: nowrap;
        }

        .el-select {
          width: 120px;
        }
      }
    }

    .enterprise-list {
      margin-bottom: 20px;

      .enterprise-row {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        position: relative;
        cursor: pointer;

        &:hover {
          .enterprise-card {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
          }
        }

        &.selected {
          .enterprise-card {
            border: 1px solid #409EFF;
            box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2);
          }
        }

        .item-checkbox {
          margin-right: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .enterprise-list-item {
          flex: 1;

          .enterprise-card {
            transition: all 0.3s;

            .card-body {
              display: flex;
              justify-content: space-between;
              align-items: stretch;

              .card-left {
                flex: 1;

                .card-header {
                  display: flex;
                  flex-wrap: wrap;
                  justify-content: flex-start;
                  align-items: center;
                  margin-bottom: 15px;
                  padding-bottom: 10px;
                  border-bottom: 1px solid #ebeef5;

                  .enterprise-name {
                    font-size: 16px;
                    font-weight: 600;
                    color: #303133;
                    margin-right: 10px;
                  }

                  .risk-tag {
                    margin-right: 5px;
                    margin-bottom: 5px;
                  }
                }

                .card-content {
                  display: flex;
                  flex-wrap: wrap;
                  width: 100%;
                  padding: 10px 0;

                  .info-item {
                    margin-bottom: 8px;
                    font-size: 13px;
                    margin-right: 20px;
                    display: flex;
                    align-items: center;
                    flex: 0 0 auto;
                    min-width: 200px;

                    &.full-width {
                      width: 100%;
                      flex-basis: 100%;
                      margin-top: 5px;
                    }

                    .label {
                      color: #909399;
                      white-space: nowrap;
                    }

                    .value {
                      color: #606266;
                      margin-left: 5px;
                      white-space: normal;
                      word-break: break-all;
                    }
                  }
                }
              }

              .card-footer {
                display: flex;
                flex-direction: row;
                justify-content: flex-end;
                align-items: center;
                padding-left: 20px;
                border-left: 1px solid #ebeef5;
                margin-left: 20px;

                .el-button {
                  margin: 0 5px;

                  &:first-child {
                    margin-left: 0;
                  }

                  &:last-child {
                    margin-right: 0;
                  }
                }
              }
            }
          }
        }
      }

      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px 15px;
        background-color: #f5f7fa;
        border-radius: 4px;

        .left-section {
          display: flex;
          align-items: center;

          .el-button {
            margin-right: 10px;

            &:last-child {
              margin-right: 0;
            }
          }
        }

        .right-section {
          display: flex;
          align-items: center;

          .batch-actions {
            display: flex;
            align-items: center;
            margin-right: 15px;

            .selected-count {
              color: #606266;
            }
          }

          .action-button {
            font-size: 14px;
            padding: 0;
            margin-left: 15px;
            color: #606266;
            display: flex;
            align-items: center;

            i {
              font-size: 16px;
              margin-right: 4px;
            }

            .action-text {
              font-size: 13px;
            }

            &:hover {
              color: #409EFF;
            }
          }
        }
      }

      .list-items {
        .list-item {
          display: flex;
          align-items: stretch;
          margin-bottom: 15px;
          border-radius: 4px;
          border: 1px solid #ebeef5;
          transition: all 0.3s;

          &:hover {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }

          &.selected {
            background-color: #f0f9eb;
            border-color: #67c23a;
          }

          .item-checkbox {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 15px;
            border-right: 1px solid #ebeef5;
          }

          .item-content {
            flex: 1;
            padding: 15px;
            cursor: pointer;

            .item-main {
              display: flex;
              flex-direction: column;
              margin-bottom: 10px;
              padding-bottom: 10px;
              border-bottom: 1px solid #ebeef5;

              .item-name {
                font-size: 16px;
                font-weight: 600;
                color: #303133;
                margin-bottom: 8px;

                .risk-tag {
                  margin-left: 10px;
                }
              }

              .item-relations {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;

                .relation-tag {
                  font-size: 12px;
                  color: #606266;
                  background-color: #f5f7fa;
                  padding: 2px 8px;
                  border-radius: 2px;
                }
              }
            }

            .item-details {
              .detail-row {
                display: flex;
                margin-bottom: 8px;

                .detail-item {
                  flex: 1;
                  display: flex;
                  font-size: 13px;

                  &.full-width {
                    flex: 0 0 100%;
                  }

                  .label {
                    color: #909399;
                    margin-right: 5px;
                    white-space: nowrap;
                  }

                  .value {
                    color: #606266;
                  }
                }
              }
            }
          }

          .item-actions {
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 0 15px;
            border-left: 1px solid #ebeef5;

            .el-button {
              margin-left: 0;
              margin-right: 0;
              padding: 8px 0;

              & + .el-button {
                margin-top: 5px;
              }
            }
          }
        }
      }

      .empty-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 50px 0;
        color: #909399;

        i {
          font-size: 48px;
          margin-bottom: 15px;
        }

        p {
          font-size: 14px;
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 30px;
    }
  }

  .display-fields-container {
    display: flex;
    flex-direction: column;

    .el-checkbox {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  @media (max-width: 768px) {
    .enterprise-content {
      .enterprise-list {
        .list-header {
          flex-direction: column;

          .left-section {
            margin-bottom: 10px;
            flex-wrap: wrap;

            .el-button {
              margin-bottom: 5px;
            }
          }

          .right-section {
            justify-content: flex-end;

            .action-button {
              margin-left: 10px;
            }
          }
        }
        .list-item {
          flex-direction: column;

          .item-checkbox {
            padding: 10px;
            border-right: none;
            border-bottom: 1px solid #ebeef5;
            justify-content: flex-start;
          }

          .item-content {
            .card-content {
              flex-direction: column;

              .info-item {
                width: 100%;
                margin-right: 0;
                min-width: auto;
              }
            }
          }

          .item-actions {
            flex-direction: row;
            padding: 10px;
            border-left: none;
            border-top: 1px solid #ebeef5;

            .el-button {
              flex: 1;

              & + .el-button {
                margin-top: 0;
                margin-left: 10px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
