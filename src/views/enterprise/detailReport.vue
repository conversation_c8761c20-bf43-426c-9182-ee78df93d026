<template>
  <div class="app-container enterprise-detail-report">
    <!-- <div class="report-header">
      <div class="header-left">
        <el-button icon="el-icon-arrow-left" type="text" @click="goBack">返回</el-button>
        <h2 class="report-title">企业详情报告</h2>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="el-icon-printer" @click="printReport">打印报告</el-button>
        <el-button type="success" icon="el-icon-download" @click="exportReport">导出报告</el-button>
      </div>
    </div> -->

    <div class="report-content" ref="reportContent">
       <el-divider id="enterprise-overview"><i class="el-icon-office-building"></i> 企业主体信息</el-divider>
      <div class="enterprise-overview">
        <el-row :gutter="20">
          <!-- 左侧：企业照片 -->
          <el-col :span="4">
            <div class="overview-left">
              <div class="enterprise-photo">
                <img v-if="enterpriseData.photo" :src="enterpriseData.photo" alt="企业照片" />
                <i v-else class="el-icon-office-building"></i>
              </div>
            </div>
          </el-col>

          <!-- 中间：企业信息 -->
          <el-col :span="14">
            <div class="overview-middle">
              <!-- 第一行：企业名称和统一社会信用代码 -->
              <div class="enterprise-header">
                <h3 class="enterprise-name">{{ enterpriseData.name }}</h3>
                <div class="credit-code">
                  <span class="label">统一社会信用代码：</span>
                  <span class="value">{{ enterpriseData.creditCode }}</span>
                </div>
              </div>

              <!-- 第二行：企业标签 -->
              <div class="enterprise-tags">
                <el-tag
                  v-for="(level, index) in enterpriseData.riskLevels"
                  :key="index"
                  plain
                  size="medium"
                  :style="{ color: getRiskLevelColor(level), borderColor: getRiskLevelColor(level), backgroundColor: getLightenedColor(level) }"
                  class="risk-tag"
                >
                  {{ getRiskLevelLabel(level) }} {{ level }}
                </el-tag>
              </div>

              <!-- 其他企业信息 -->
              <div class="enterprise-info">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="info-item">
                      <span class="label">企业类型：</span>
                      <span class="value">{{ enterpriseData.companyType }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <span class="label">公安备案号：</span>
                      <span class="value">{{ enterpriseData.policeRecordNumber }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <span class="label">企业规模：</span>
                      <span class="value">{{ enterpriseData.scale }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <span class="label">法定代表人：</span>
                      <span class="value">{{ enterpriseData.legalPerson }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <span class="label">联系电话：</span>
                      <span class="value">{{ enterpriseData.contactPhone }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <span class="label">企业状态：</span>
                      <span class="value" :style="{ color: getStateColor(enterpriseData.state) }">{{ enterpriseData.status }}</span>
                    </div>
                  </el-col>
                  <el-col :span="24">
                    <div class="info-item">
                      <span class="label">注册地址：</span>
                      <span class="value">{{ enterpriseData.address }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-col>

          <!-- 右侧：营业执照照片 -->
          <el-col :span="6">
            <div class="overview-right">
              <div class="license-photo">
                <div class="photo-title">营业执照</div>
                <div class="photo-container">
                  <img v-if="enterpriseData.licensePhoto" :src="enterpriseData.licensePhoto" alt="营业执照" />
                  <i v-else class="el-icon-picture-outline"></i>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <!-- <el-divider><i class="el-icon-office-building"></i> 企业基础信息</el-divider> -->

      <!-- 企业基础信息 -->
      <div id="enterprise-basic" class="detail-section enterprise-basic-section">
        <div class="section-header" @click="toggleEnterpriseBasic">
          <h3 class="section-title">企业基础信息</h3>
          <div class="section-actions">
            <span class="count-badge">3</span>
            <i :class="enterpriseBasicExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </div>
        </div>

        <div class="section-content" v-show="enterpriseBasicExpanded">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">成立日期：</span>
                <span class="value">{{ enterpriseData.establishDate || '2015-06-15' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">注册资本：</span>
                <span class="value">{{ enterpriseData.registeredCapital || '1000万元' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">所属区域：</span>
                <span class="value">{{ enterpriseData.region }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">负责人：</span>
                <span class="value">{{ enterpriseData.manager }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">负责人电话：</span>
                <span class="value">{{ enterpriseData.managerPhone }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">企业状态：</span>
                <span class="value" :style="{ color: getStateColor(enterpriseData.state) }">{{ enterpriseData.status }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <span class="label">经营范围：</span>
                <span class="value">{{ enterpriseData.businessScope || '计算机软硬件技术开发、技术转让、技术咨询、技术服务；数据处理；应用软件服务；销售计算机软硬件及辅助设备、电子产品、通讯设备。' }}</span>
              </div>
            </el-col>
          </el-row>

          <!-- 企业法人信息子模块 -->
          <div class="subsection legal-person-section">
            <div class="subsection-header" @click="toggleLegalPerson">
              <h4 class="subsection-title">企业法人信息</h4>
              <div class="subsection-actions">
                <span class="count-badge">1</span>
                <i :class="legalPersonExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
              </div>
            </div>

            <div class="subsection-content" v-show="legalPersonExpanded">
              <div class="person-item">
                <el-row :gutter="20" class="person-info">
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">法人姓名：</span>
                      <span class="value">{{ legalPerson.name }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">法人联系电话：</span>
                      <span class="value">{{ legalPerson.phone }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">证件类型：</span>
                      <span class="value">{{ legalPerson.idType }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">证件号码：</span>
                      <span class="value">{{ legalPerson.idNumber }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">证件有效期：</span>
                      <span class="value">{{ legalPerson.expiryDate }}</span>
                    </div>
                  </el-col>
                </el-row>

                <div class="person-photos">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <div class="photo-item">
                        <div class="photo-title">法人证件(人像)</div>
                        <div class="photo-container">
                          <img v-if="legalPerson.portraitPhoto" :src="legalPerson.portraitPhoto" alt="法人证件(人像)" />
                          <i v-else class="el-icon-picture-outline"></i>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="photo-item">
                        <div class="photo-title">法人证件照(国徽)</div>
                        <div class="photo-container">
                          <img v-if="legalPerson.nationalEmblemPhoto" :src="legalPerson.nationalEmblemPhoto" alt="法人证件照(国徽)" />
                          <i v-else class="el-icon-picture-outline"></i>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="photo-item">
                        <div class="photo-title">法人证件(手持)</div>
                        <div class="photo-container">
                          <img v-if="legalPerson.holdingPhoto" :src="legalPerson.holdingPhoto" alt="法人证件(手持)" />
                          <i v-else class="el-icon-picture-outline"></i>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>
          </div>

          <!-- 企业责任人信息子模块 -->
          <div class="subsection responsible-person-section">
            <div class="subsection-header" @click="toggleResponsiblePerson">
              <h4 class="subsection-title">企业责任人信息</h4>
              <div class="subsection-actions">
                <span class="count-badge">1</span>
                <i :class="responsiblePersonExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
              </div>
            </div>

            <div class="subsection-content" v-show="responsiblePersonExpanded">
              <div class="person-item">
                <el-row :gutter="20" class="person-info">
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">负责人：</span>
                      <span class="value">{{ responsiblePerson.name }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">负责人联系电话：</span>
                      <span class="value">{{ responsiblePerson.phone }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">证件类型：</span>
                      <span class="value">{{ responsiblePerson.idType }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">证件号码：</span>
                      <span class="value">{{ responsiblePerson.idNumber }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">证件有效期：</span>
                      <span class="value">{{ responsiblePerson.expiryDate }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">办公室电话：</span>
                      <span class="value">{{ responsiblePerson.officePhone }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">电子邮件：</span>
                      <span class="value">{{ responsiblePerson.email }}</span>
                    </div>
                  </el-col>
                  <el-col :span="24">
                    <div class="info-item">
                      <span class="label">常驻地址：</span>
                      <span class="value">{{ responsiblePerson.address }}</span>
                    </div>
                  </el-col>
                </el-row>

                <div class="person-photos">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <div class="photo-item">
                        <div class="photo-title">法人证件(人像)</div>
                        <div class="photo-container">
                          <img v-if="responsiblePerson.portraitPhoto" :src="responsiblePerson.portraitPhoto" alt="法人证件(人像)" />
                          <i v-else class="el-icon-picture-outline"></i>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="photo-item">
                        <div class="photo-title">法人证件照(国徽)</div>
                        <div class="photo-container">
                          <img v-if="responsiblePerson.nationalEmblemPhoto" :src="responsiblePerson.nationalEmblemPhoto" alt="法人证件照(国徽)" />
                          <i v-else class="el-icon-picture-outline"></i>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="photo-item">
                        <div class="photo-title">法人证件(手持)</div>
                        <div class="photo-container">
                          <img v-if="responsiblePerson.holdingPhoto" :src="responsiblePerson.holdingPhoto" alt="法人证件(手持)" />
                          <i v-else class="el-icon-picture-outline"></i>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-divider>
        <span
          class="collapsible-title"
          @click="toggleBusinessInfoExpanded"
          style="cursor: pointer; user-select: none;"
        >
          <i class="el-icon-monitor"></i>
          企业业务信息
          <i
            :class="businessInfoExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
            style="margin-left: 8px; font-size: 12px;"
          ></i>
        </span>
      </el-divider>

      <!-- 企业业务信息模块内容 -->
      <div id="business-info" v-show="businessInfoExpanded" class="business-info-content">
        <!-- 备案信息卡片部分 -->
        <div class="detail-section record-cards-section">
        <el-row :gutter="20">
          <!-- 等保备案卡片 -->
          <el-col :span="6">
            <div class="record-card">
              <div class="card-title">等保备案</div>
              <div class="card-content">
                <div class="card-item">
                  <span class="item-label">当前备案等级：</span>
                  <span class="item-value">{{ recordCards.securityRecord.level }}</span>
                </div>
                <div class="card-item">
                  <span class="item-label">有效期：</span>
                  <span class="item-value">{{ recordCards.securityRecord.expiryDate }}</span>
                </div>
                <div class="card-item">
                  <span class="item-label">测评结果：</span>
                  <span class="item-value" :style="{ color: getStatusColor(recordCards.securityRecord.result) }">
                    {{ recordCards.securityRecord.result }}
                  </span>
                </div>
              </div>
            </div>
          </el-col>

          <!-- APP备案卡片 -->
          <el-col :span="6">
            <div class="record-card">
              <div class="card-title">APP备案</div>
              <div class="card-content">
                <div class="card-item">
                  <span class="item-label">备案号：</span>
                  <span class="item-value">{{ recordCards.appRecord.recordNumber }}</span>
                </div>
                <div class="card-item">
                  <span class="item-label">运营状态：</span>
                  <span class="item-value" :style="{ color: getStatusColor(recordCards.appRecord.operationStatus) }">
                    {{ recordCards.appRecord.operationStatus }}
                  </span>
                </div>
                <div class="card-item">
                  <span class="item-label">更新时间：</span>
                  <span class="item-value">{{ recordCards.appRecord.updateTime }}</span>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 小程序备案卡片 -->
          <el-col :span="6">
            <div class="record-card">
              <div class="card-title">小程序备案</div>
              <div class="card-content">
                <div class="card-item">
                  <span class="item-label">备案号：</span>
                  <span class="item-value">{{ recordCards.miniProgramRecord.recordNumber }}</span>
                </div>
                <div class="card-item">
                  <span class="item-label">运营状态：</span>
                  <span class="item-value" :style="{ color: getStatusColor(recordCards.miniProgramRecord.operationStatus) }">
                    {{ recordCards.miniProgramRecord.operationStatus }}
                  </span>
                </div>
                <div class="card-item">
                  <span class="item-label">更新时间：</span>
                  <span class="item-value">{{ recordCards.miniProgramRecord.updateTime }}</span>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 数字资产备案卡片 -->
          <el-col :span="6">
            <div class="record-card">
              <div class="card-title">数字资产备案</div>
              <div class="card-content">
                <div class="card-item">
                  <span class="item-label">IP地址段：</span>
                  <span class="item-value">{{ recordCards.digitalAssetRecord.ipRange }}</span>
                </div>
                <div class="card-item">
                  <span class="item-label">IDC机房位置：</span>
                  <span class="item-value">{{ recordCards.digitalAssetRecord.idcLocation }}</span>
                </div>
                <div class="card-item">
                  <span class="item-label">带宽用户量：</span>
                  <span class="item-value">{{ recordCards.digitalAssetRecord.bandwidthUsers }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 关联电子屏部分 -->
      <div class="detail-section electronic-screens-section">
        <h3 class="section-title">
          关联电子屏
          <span class="count-badge">({{ screenData.length }})</span>
        </h3>

        <div class="electronic-screens-table">
          <el-table
            :data="screenData"
            style="width: 100%"
            border
            stripe
            height="450"
          >
            <!-- 固定在左侧的列 -->
            <el-table-column
              type="index"
              label="序号"
              width="60"
              fixed="left"
              align="center"
            ></el-table-column>

            <el-table-column
              prop="name"
              label="电子屏名称"
              width="180"
              fixed="left"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              prop="policeStation"
              label="所属派出所"
              width="150"
              fixed="left"
              show-overflow-tooltip
            ></el-table-column>

            <!-- 中间可滚动的列 -->
            <el-table-column
              prop="contactPerson"
              label="责任单位联系人"
              width="180"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              prop="keyArea"
              label="重点区域"
              width="200"
            >
              <template slot-scope="scope">
                <el-tag
                  v-for="(area, index) in scope.row.keyArea"
                  :key="index"
                  size="mini"
                  style="color: #4584FF; background-color: #EBF5FF; border-color: #4584FF; margin-right: 5px; margin-bottom: 5px;"
                >
                  {{ area }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column
              prop="specialProject"
              label="所属专项"
              width="200"
            >
              <template slot-scope="scope">
                <el-tag
                  v-for="(project, index) in scope.row.specialProject"
                  :key="index"
                  size="mini"
                  style="color: #19BB77; background-color: #E8F9F2; border-color: #19BB77; margin-right: 5px; margin-bottom: 5px;"
                >
                  {{ project }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column
              prop="belongingPlace"
              label="归属场所"
              width="120"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              prop="networkSystem"
              label="联网系统"
              width="180"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              prop="inspectionCount"
              label="最近三月检查数"
              width="150"
              align="center"
            ></el-table-column>

            <el-table-column
              prop="updateTime"
              label="更新时间"
              width="120"
              show-overflow-tooltip
            ></el-table-column>

            <!-- 固定在右侧的操作列 -->
            <el-table-column
              label="操作"
              width="180"
              fixed="right"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  @click="viewScreenDetail(scope.row)"
                >详情</el-button>
                <el-button
                  size="mini"
                  type="success"
                  @click="editScreen(scope.row)"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="danger"
                  @click="deleteScreen(scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 企业网络安全评估部分 -->
      <el-divider id="security-assessment"><i class="el-icon-lock"></i> 企业网络安全评估</el-divider>

      <div class="detail-section security-assessment-section">
        <h3 class="section-title">企业网络安全评估</h3>
        <el-row :gutter="20" style="background-color: #4584FF;">
          <!-- 左侧：综合评分 -->
          <el-col :span="6">
            <div class="security-score-panel">
              <div class="score-title">企业网络安全综合评分</div>
              <div class="score-value" style="color:#FF8181;">
                {{ securityAssessment.totalScore }}
              </div>
              <div class="score-change">
                <span>较上月</span>
                <i :class="getChangeIcon(securityAssessment.lastMonthChange).icon"
                   :style="{ color: getChangeIcon(securityAssessment.lastMonthChange).color }"></i>
                <span :style="{ color: getChangeIcon(securityAssessment.lastMonthChange).color }">
                  {{ Math.abs(securityAssessment.lastMonthChange) }}
                </span>
              </div>
              <div class="score-compare">
                高出平均分 <span>{{ securityAssessment.aboveAverage }}</span>
              </div>
              <div class="score-stars">
                <i v-for="n in 5" :key="n" class="el-icon-star-on"
                   :style="{ color: n <= securityAssessment.starCount ? '#F7BA2A' : '#EBEEF5' }"></i>
              </div>
              <div class="risk-level" :style="{ color: getSecurityRiskColor(securityAssessment.riskLevel) }">
                {{ securityAssessment.riskLevel }}
              </div>
            </div>
          </el-col>

          <!-- 中间：雷达图 -->
          <el-col :span="10">
            <div class="security-radar-chart">
              <div id="securityRadarChart" style="width: 100%; height: 350px;"></div>
            </div>
          </el-col>

          <!-- 右侧：评估详情 -->
          <el-col :span="8">
            <div class="security-assessment-details">
              <div class="assessment-overview">
                <h4>整体评估</h4>
                <p>{{ enterpriseData.name }}属于{{ securityAssessment.riskLevel }}公司，各项指标表现良好，特别是资质与合规性方面，表现优异。但在人员与内部安全管理和舆情与社会影响方面需要注意。</p>
              </div>

              <div class="assessment-items">
                <div v-for="(item, index) in securityAssessment.radarData" :key="index" class="assessment-item">
                  <div class="item-title">{{ item.name }}：</div>
                  <div class="item-content">
                    综合评分为<span class="item-score">{{ item.value }}</span>，
                    较上月<span :style="{ color: getChangeIcon(item.lastMonthChange).color }">
                      {{ item.lastMonthChange > 0 ? '上升' : '降低' }} {{ Math.abs(item.lastMonthChange) }}
                    </span>，
                    <span :class="['item-status',
                      item.status === '表现优秀' ? 'status-good' :
                      item.status === '需持续关注' ? 'status-warning' : 'status-danger']">
                      {{ item.status }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      </div>
      <!-- 企业业务信息模块内容结束 -->

      <!-- 历史合规记录模块 -->
      <el-divider id="compliance-records"><i class="el-icon-document-checked"></i> 历史合规记录</el-divider>
      <div class="detail-section compliance-records-section">
        <h3 class="section-title">历史合规记录</h3>
        <el-row :gutter="20">
          <el-col :span="8" v-for="record in complianceRecords" :key="record.id">
            <div class="compliance-card">
              <div class="card-content">
                <div class="card-left">
                  <div class="record-type">{{ record.type }}</div>
                  <div class="record-stats">
                    <span class="total-count">{{ record.totalCount }}</span>
                    <el-tag size="mini" type="info" class="recent-period">{{ record.recentPeriod }}</el-tag>
                  </div>
                </div>
                <div class="card-right">
                  <!-- 第一个卡片：立即现场检查记录 -->
                  <template v-if="record.id === 1">
                    <div class="check-time">检查时间：{{ record.lastCheckTime }}</div>
                    <div class="problem-stats">
                      <span class="problem-count">问题数量：{{ record.problemCount }}</span>
                      <div class="resolution-status">
                        <span class="status-label">整改情况：</span>
                        <el-tag v-if="record.unresolved > 0" size="mini" type="danger" class="status-tag">
                          未整改 {{ record.unresolved }}
                        </el-tag>
                        <el-tag v-if="record.resolved > 0" size="mini" type="success" class="status-tag">
                          已整改 {{ record.resolved }}
                        </el-tag>
                      </div>
                    </div>
                  </template>

                  <!-- 第二个和第三个卡片：行政处罚记录和行政处置 -->
                  <template v-else>
                    <div class="action-time">{{ record.timeLabel }}：{{ record.lastTime }}</div>
                    <div class="action-details">
                      <div class="action-reason">{{ record.reasonLabel }}：{{ record.reason }}</div>
                      <div class="action-result">{{ record.resultLabel }}：{{ record.result }}</div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 历史合规记录详细列表 -->
        <div class="compliance-detail-list">
          <el-table
            :data="complianceDetailList"
            style="width: 100%"
            border
            :header-cell-style="{ background: '#EBF1FF', color: '#303133' }"
            :row-style="{ height: 'auto' }"
          >
            <el-table-column
              prop="decisionNumber"
              label="行政处罚决定书编号"
              width="200"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="decisionDate"
              label="处罚决定时间"
              width="150"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="caseDescription"
              label="简要案情"
              min-width="400"
            >
              <template slot-scope="scope">
                <div class="case-description">
                  {{ scope.row.caseDescription }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 当前数字资产清单模块 -->
      <el-divider id="digital-assets"><i class="el-icon-data-board"></i> 当前数字资产清单</el-divider>
      <div class="detail-section digital-asset-section">
        <h3 class="section-title">当前数字资产清单</h3>
        <div class="asset-cards-container">
          <div
            v-for="(asset, index) in digitalAssetList"
            :key="index"
            class="asset-card"
            :style="{
              backgroundColor: asset.color,
              borderColor: asset.borderColor,
              color: asset.textColor
            }"
          >
            <div class="asset-count">{{ asset.count }}</div>
            <div class="asset-name">{{ asset.name }}</div>
          </div>
        </div>
        <div class="asset-cards-detail">
          <ul>
            <li class="detail-item">
              <h5 class="cards-detail-tit">IP地址段（2）</h5>
              <div class="carousel-wrapper">
                <ul class="carousel-container" ref="ipCarousel">
                  <li>
                    <div class="label-fields-container">
                      <div class="label-field">
                        <label class="left-lable">起始IP：</label>
                        <span>***********</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">终止IP：</label>
                        <span>***********98</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">所属网络：</label>
                        <span>中国电信</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">更新时间：</label>
                        <span>2024.4.28 12:42:36</span>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="label-fields-container">
                      <div class="label-field">
                        <label class="left-lable">起始IP：</label>
                        <span>***********</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">终止IP：</label>
                        <span>*************</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">所属网络：</label>
                        <span>中国联通</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">更新时间：</label>
                        <span>2024.4.28 19:55:22</span>
                      </div>
                    </div>
                  </li>
                </ul>
                <div class="carousel-indicators">
                  <span
                    v-for="(item, index) in 2"
                    :key="index"
                    :class="['indicator', { active: currentSlide === index }]"
                    @click="goToSlide(index)"
                  ></span>
                </div>
              </div>
            </li>
            <li class="detail-item">
              <h5 class="cards-detail-tit">IDC机房（0）</h5>
              <div class="carousel-wrapper">
                <ul class="carousel-container">
                  <li>
                    <div class="label-fields-container">
                      <div class="label-field">
                        <label class="left-lable">机房名称：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">机房负责人：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">机房地址：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">所属辖区：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">机房类型：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">服务器数量：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">机架数量：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">宽带总量：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">更新时间：</label>
                        <span>——</span>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </li>
            <li class="detail-item">
              <h5 class="cards-detail-tit">宽带用户总数（0）</h5>
              <div class="carousel-wrapper">
                <ul class="carousel-container">
                  <li>
                    <div class="label-fields-container">
                      <div class="label-field">
                        <label class="left-lable">开户人姓名：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">装机电话：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">装机地点：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">用户账号：</label>
                        <span>——</span>
                      </div>
                      <div class="label-field">
                        <label class="left-lable">更新时间：</label>
                        <span>——</span>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </li>
          </ul>
        </div>
      </div>

        <!-- 月度更新情况模块 -->
      <el-divider id="monthly-status"><i class="el-icon-data-analysis"></i> 月度更新情况</el-divider>
      <!-- 月度情况组件 -->
      <monthly-status-module />

      <!-- 资产情况组件 -->
      <div id="asset-changes">
        <asset-change-module />
      </div>

    </div>

    <!-- 锚点导航 -->
    <div
      class="anchor-navigation"
      v-show="showAnchorNav"
      :style="{ top: anchorNavPosition.y + 'px', right: anchorNavPosition.x + 'px' }"
    >
      <div class="anchor-nav-container">
        <div
          class="anchor-nav-header"
          @mousedown="startDrag"
          :class="{ dragging: isDragging }"
        >
          <span class="nav-title">页面导航</span>
          <i class="el-icon-close" @click="hideAnchorNav"></i>
        </div>
        <ul class="anchor-nav-list">
          <li
            v-for="anchor in anchorList"
            :key="anchor.id"
            :class="['anchor-nav-item', { active: currentActiveAnchor === anchor.id }]"
            @click="scrollToAnchor(anchor.id)"
          >
            <i :class="anchor.icon"></i>
            <span class="anchor-text">{{ anchor.text }}</span>
          </li>
        </ul>
      </div>
      <div class="anchor-nav-toggle" @click="toggleAnchorNav">
        <i :class="showAnchorNav ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"></i>
      </div>
    </div>
  </div>
</template>











<script>
import { getEnterpriseDetail, getRiskLevelOptions } from '@/api/enterprise'
import * as echarts from 'echarts'
import AssetChangeModule from '@/components/AssetChangeModule'
import MonthlyStatusModule from '@/components/MonthlyStatusModule'

export default {
  name: 'EnterpriseDetailReport',
  components: {
    AssetChangeModule,
    MonthlyStatusModule
  },
  props: {
    id: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      activeTab: 'basic',
      relationTab: 'screen',
      enterpriseData: {},
      riskLevelOptions: [],
      riskFactors: [],
      supervisionRecords: [],
      screenData: [],
      securityData: [],
      websiteData: [],
      operatorData: [],
      netbarData: [],
      enterpriseBasicExpanded: true, // 控制企业基础信息模块展开/收起状态
      legalPersonExpanded: true, // 控制法人信息展开/收起状态
      responsiblePersonExpanded: true, // 控制责任人信息展开/收起状态
      businessInfoExpanded: true, // 控制企业业务信息模块展开/收起状态
      legalPerson: {}, // 存储法人信息（对象模式）
      responsiblePerson: {}, // 存储责任人信息
      riskDistribution: [
        { name: '经营风险', percentage: 65, color: '#F56C6C' },
        { name: '安全风险', percentage: 45, color: '#E6A23C' },
        { name: '环保风险', percentage: 20, color: '#67C23A' },
        { name: '信用风险', percentage: 30, color: '#409EFF' }
      ],
      // 网络安全评估数据
      securityAssessment: {
        totalScore: 85,
        lastMonthChange: 5,
        aboveAverage: 12,
        riskLevel: '低风险',
        starCount: 4,
        radarData: [
          { name: '资质与合规情况', value: 3.2, lastMonthChange: -0.4, status: '需持续关注' },
          { name: '历史安全事件与威胁情报', value: 2.4, lastMonthChange: -4.4, status: '请重点关注' },
          { name: '互联网暴露面与资产风险', value: 1.0, lastMonthChange: -0.4, status: '请重点关注' },
          { name: '运营安全能力', value: 1.0, lastMonthChange: 0.1, status: '表现优秀' },
          { name: '舆情与社会影响', value: 0.2, lastMonthChange: -0.4, status: '需持续关注' },
          { name: '人员与内部安全管理', value: 0.2, lastMonthChange: -4.4, status: '请重点关注' },
          { name: '数据质量管理', value: 0.6, lastMonthChange: -0.4, status: '请重点关注' }
        ]
      },
      securityChart: null,
      // 备案信息卡片数据
      recordCards: {
        securityRecord: {
          level: '三级',
          expiryDate: '2022.08.18 15:20',
          result: '通过'
        },
        appRecord: {
          recordNumber: 'xxxxx',
          operationStatus: '正常运营',
          updateTime: '2022.08.18 15:20'
        },
        miniProgramRecord: {
          recordNumber: '京89891',
          operationStatus: '正常运营',
          updateTime: '2022.08.18 15:20'
        },
        digitalAssetRecord: {
          ipRange: '**************-255',
          idcLocation: '2022.08.18 15:20',
          bandwidthUsers: '98981'
        }
      },
      // 历史合规记录数据
      complianceRecords: [
        {
          id: 1,
          type: '立即现场检查记录',
          totalCount: 12,
          recentPeriod: '近半年两次',
          lastCheckTime: '2023-10-15',
          problemCount: 8,
          unresolved: 2,
          resolved: 6
        },
        {
          id: 2,
          type: '累计行政处罚记录',
          totalCount: 8,
          recentPeriod: '近半年1次',
          lastTime: '2023-11-20',
          reason: '在网络发布恶意言论',
          result: '罚款2000，停业整改',
          timeLabel: '行政处罚时间',
          reasonLabel: '事由',
          resultLabel: '处罚结果'
        },
        {
          id: 3,
          type: '累计行政处置',
          totalCount: 3,
          recentPeriod: '近一年1次',
          lastTime: '2023-09-08',
          reason: '系统安全漏洞',
          result: '勒令整改',
          timeLabel: '行政处置时间',
          reasonLabel: '事由',
          resultLabel: '核查结果'
        }
      ],
      // 历史合规记录详细列表数据
      complianceDetailList: [
        {
          id: 1,
          decisionNumber: '京网安罚字2018第001号',
          decisionDate: '2018-07-15',
          caseDescription: '未履行用户隐私保护责任，导致用户数据泄露。根据《中华人民共和国网络安全法》第五十四条规定，企业在处理个人信息时应当采取有效措施，确保用户信息安全。然而，当事公司未采取足够措施以确保用户个人数据的安全，导致部分用户隐私信息泄露，造成了不良社会影响。根据相关法规，责令其加强数据安全管理，并及时向社会公开整改措施。'
        },
        {
          id: 2,
          decisionNumber: '京网安罚字2022第011号',
          decisionDate: '2022-06-08',
          caseDescription: '2022年6月8日，北京北控曙光大数据股份有限公司在网络安全检查中，因未履行用户告知义务，违规收集用户信息，严重违反了《网络安全法》第四十四条规定，企业必须告知用户信息收集的目的、方式和使用范围，且需征得用户同意。此行为对用户隐私保护构成威胁，并可能引发法律风险。'
        }
      ],
      // 当前数字资产清单数据
      digitalAssetList: [
        { name: '上级接入商', count: 3, color: '#E3F2FD', borderColor: '#1976D2', textColor: '#1976D2' },
        { name: 'IP地址段', count: 101, color: '#F3E5F5', borderColor: '#7B1FA2', textColor: '#7B1FA2' },
        { name: 'IDC机房', count: 15, color: '#E8F5E8', borderColor: '#388E3C', textColor: '#388E3C' },
        { name: '宽带用户', count: 3665, color: '#FFF3E0', borderColor: '#F57C00', textColor: '#F57C00' },
        { name: '域名注册', count: 3665, color: '#FCE4EC', borderColor: '#C2185B', textColor: '#C2185B' },
        { name: '虚拟运营', count: 3665, color: '#F1F8E9', borderColor: '#689F38', textColor: '#689F38' },
        { name: 'CDN服务', count: 3665, color: '#E0F2F1', borderColor: '#00796B', textColor: '#00796B' },
        { name: '云服务', count: 3665, color: '#FFF8E1', borderColor: '#FBC02D', textColor: '#FBC02D' },
        { name: '专线用户', count: 3665, color: '#EFEBE9', borderColor: '#5D4037', textColor: '#5D4037' }
      ],
      // 轮播相关数据
      currentSlide: 0,
      autoSlideTimer: null,

      // 锚点导航相关数据
      showAnchorNav: true,
      currentActiveAnchor: 'enterprise-overview',
      anchorList: [
        { id: 'enterprise-overview', text: '企业主体信息', icon: 'el-icon-office-building' },
        { id: 'enterprise-basic', text: '企业基础信息', icon: 'el-icon-user' },
        { id: 'business-info', text: '企业业务信息', icon: 'el-icon-monitor' },
        { id: 'security-assessment', text: '网络安全评估', icon: 'el-icon-lock' },
        { id: 'compliance-records', text: '历史合规记录', icon: 'el-icon-document-checked' },
        { id: 'digital-assets', text: '数字资产清单', icon: 'el-icon-data-board' },
        { id: 'monthly-status', text: '月度更新情况', icon: 'el-icon-data-analysis' },
        { id: 'asset-changes', text: '资产变化情况', icon: 'el-icon-trend-charts' }
      ],

      // 拖拽相关数据
      isDragging: false,
      anchorNavPosition: {
        x: 0, // 距离右边的距离
        y: 0  // 距离顶部的距离，初始为0，mounted时会设置为50%
      },
      dragStartPos: {
        x: 0,
        y: 0
      }
    }
  },
  created() {
    this.fetchEnterpriseDetail()
    this.fetchRiskLevelOptions()
  },
  mounted() {
    this.$nextTick(() => {
      this.initSecurityRadarChart()
      this.initCarouselContainers()
      this.startAutoSlide()
      this.initScrollListener()
      this.initAnchorNavPosition()
    })
  },
  beforeDestroy() {
    // 销毁图表实例，避免内存泄漏
    if (this.securityChart) {
      this.securityChart.dispose()
      this.securityChart = null
    }
    // 清理轮播定时器
    if (this.autoSlideTimer) {
      clearInterval(this.autoSlideTimer)
      this.autoSlideTimer = null
    }
    // 清理滚动监听
    window.removeEventListener('scroll', this.handleScroll)
    // 清理拖拽监听
    document.removeEventListener('mousemove', this.handleDrag)
    document.removeEventListener('mouseup', this.stopDrag)
  },
  methods: {
    // 获取企业详情
    fetchEnterpriseDetail() {
      const id = this.id || this.$route.params.id || 1

      getEnterpriseDetail(id).then(response => {
        if (response.code === 200) {
          this.enterpriseData = response.data

          // 生成风险因素数据
          this.generateRiskFactors()

          // 生成监管记录数据
          this.generateSupervisionRecords()

          // 生成关联数据
          this.generateRelationData()
        } else {
          this.$message.error('获取企业详情失败')
        }
      }).catch(error => {
        console.error('获取企业详情失败', error)
        this.$message.error('获取企业详情失败')
      })
    },

    // 获取风险级别选项
    fetchRiskLevelOptions() {
      getRiskLevelOptions().then(response => {
        if (response.code === 200) {
          this.riskLevelOptions = response.data
        }
      }).catch(error => {
        console.error('获取风险级别选项失败', error)
      })
    },

    // 生成风险因素数据
    generateRiskFactors() {
      this.riskFactors = [
        { category: '经营风险', description: '企业经营状况不稳定，近期有经营异常记录', level: '中风险' },
        { category: '安全风险', description: '企业存在安全隐患，需要加强安全管理', level: '高风险' },
        { category: '环保风险', description: '企业环保设施完善，环保意识较强', level: '低风险' },
        { category: '信用风险', description: '企业信用记录良好，无严重失信行为', level: '低风险' }
      ]
    },

    // 生成监管记录数据
    generateSupervisionRecords() {
      this.supervisionRecords = [
        {
          date: '2023-04-15',
          type: 'warning',
          title: '安全检查',
          content: '发现企业存在安全隐患，责令限期整改',
          result: '已整改'
        },
        {
          date: '2022-11-20',
          type: 'success',
          title: '环保检查',
          content: '企业环保设施运行正常，无环保违规行为',
          result: '合格'
        },
        {
          date: '2022-08-05',
          type: 'danger',
          title: '投诉处理',
          content: '收到关于企业噪音扰民的投诉，进行现场调查',
          result: '责令整改并处罚'
        },
        {
          date: '2022-03-12',
          type: 'info',
          title: '日常巡查',
          content: '对企业进行日常巡查，检查经营情况',
          result: '正常'
        }
      ]
    },

    // 生成关联数据
    generateRelationData() {
      // 电子屏数据
      this.screenData = [
        {
          id: 1,
          name: '通州区政务大厅LED屏',
          policeStation: '通州区北苑派出所',
          contactPerson: '张三-13800138001',
          keyArea: ['政务服务区', '重点监管区'],
          specialProject: ['平安城市', '智慧城市'],
          belongingPlace: '政务大厅',
          networkSystem: '城市公共信息发布系统',
          inspectionCount: 12,
          updateTime: '2023-05-15',
          location: '北京市通州区XX路XX号门口',
          size: '3m×5m',
          status: '正常'
        },
        {
          id: 2,
          name: '通州区商业街LED屏',
          policeStation: '通州区梨园派出所',
          contactPerson: '李四-13800138002',
          keyArea: ['商业区', '人流密集区'],
          specialProject: ['平安商圈', '数字城市'],
          belongingPlace: '商业街',
          networkSystem: '商业广告发布系统',
          inspectionCount: 8,
          updateTime: '2023-06-20',
          location: '北京市通州区XX路XX号路口',
          size: '4m×6m',
          status: '正常'
        },
        {
          id: 3,
          name: '通州区文化广场LED屏',
          policeStation: '通州区玉桥派出所',
          contactPerson: '王五-13800138003',
          keyArea: ['文化娱乐区', '公共场所'],
          specialProject: ['文化宣传', '智慧文旅'],
          belongingPlace: '文化广场',
          networkSystem: '文化宣传系统',
          inspectionCount: 5,
          updateTime: '2023-07-10',
          location: '北京市通州区XX路XX号广场',
          size: '5m×8m',
          status: '维修中'
        },
        {
          id: 4,
          name: '通州区交通枢纽LED屏',
          policeStation: '通州区马驹桥派出所',
          contactPerson: '赵六-13800138004',
          keyArea: ['交通枢纽', '人流密集区'],
          specialProject: ['智慧交通', '平安出行'],
          belongingPlace: '公交站',
          networkSystem: '交通信息发布系统',
          inspectionCount: 15,
          updateTime: '2023-04-25',
          location: '北京市通州区XX路XX号交通枢纽',
          size: '2.5m×4m',
          status: '正常'
        },
        {
          id: 5,
          name: '通州区医院LED屏',
          policeStation: '通州区永顺派出所',
          contactPerson: '钱七-13800138005',
          keyArea: ['医疗区', '公共场所'],
          specialProject: ['智慧医疗', '健康城市'],
          belongingPlace: '医院大厅',
          networkSystem: '医疗信息发布系统',
          inspectionCount: 6,
          updateTime: '2023-08-05',
          location: '北京市通州区XX路XX号医院',
          size: '2m×3m',
          status: '正常'
        }
      ]

      // 等保备案数据
      this.securityData = [
        { id: 'DJ-2022-001', name: '企业内部管理系统', level: '三级', date: '2022-05-20' },
        { id: 'DJ-2022-002', name: '企业官网系统', level: '二级', date: '2022-05-20' },
        { id: 'DJ-2022-003', name: '企业数据中心', level: '三级', date: '2022-05-20' }
      ]

      // 网站备案数据
      this.websiteData = [
        { domain: 'www.example.com', icp: '京ICP备12345678号-1', type: '企业官网', date: '2020-03-15' },
        { domain: 'mall.example.com', icp: '京ICP备12345678号-2', type: '电子商务', date: '2020-03-15' }
      ]

      // 运营商数据
      this.operatorData = [
        { name: '中国移动', type: '宽带业务', contact: '李四', phone: '13800138000' },
        { name: '中国联通', type: '专线业务', contact: '王五', phone: '13900139000' }
      ]

      // 网吧数据
      this.netbarData = [
        { name: 'XX网咖', address: '北京市通州区XX路XX号', license: '**********', status: '正常营业' },
        { name: 'XX电竞馆', address: '北京市通州区XX路XX号', license: '**********', status: '暂停营业' }
      ]

      // 生成法人信息和责任人信息数据
      this.generatePersonsData();
    },

    // 生成法人信息和责任人信息数据
    generatePersonsData() {
      // 生成法人信息（单个对象）
      const legalExpiryDate = new Date();
      legalExpiryDate.setFullYear(legalExpiryDate.getFullYear() + 10); // 证件有效期10年

      this.legalPerson = {
        name: this.enterpriseData.legalPerson || '张三',
        phone: this.enterpriseData.contactPhone || '13800138001',
        idType: '身份证',
        idNumber: '110101198001011234',
        expiryDate: legalExpiryDate.toISOString().split('T')[0],
        portraitPhoto: null, // 暂无证件照片用默认图片代替
        nationalEmblemPhoto: null,
        holdingPhoto: null
      };

      // 生成责任人信息
      const responsibleExpiryDate = new Date();
      responsibleExpiryDate.setFullYear(responsibleExpiryDate.getFullYear() + 8); // 证件有效期8年

      this.responsiblePerson = {
        name: '李四',
        phone: '13900139001',
        idType: '身份证',
        idNumber: '110101198505055678',
        expiryDate: responsibleExpiryDate.toISOString().split('T')[0],
        portraitPhoto: null, // 暂无证件照片用默认图片代替
        nationalEmblemPhoto: null,
        holdingPhoto: null,
        officePhone: '010-12345678',
        email: '<EMAIL>',
        address: '北京市通州区XX路XX号XX小区XX号楼XX单元XX室'
      };
    },

    // 根据风险等级获取标签类型
    getRiskLevelType(level) {
      switch(level) {
        case '高风险': return 'danger'
        case '中风险': return 'warning'
        case '低风险': return 'success'
        default: return 'info'
      }
    },

    // 获取时间线项目类型
    getTimelineItemType(type) {
      switch(type) {
        case 'success': return 'success'
        case 'warning': return 'warning'
        case 'danger': return 'danger'
        default: return 'primary'
      }
    },

    // 获取风险等级颜色
    getRiskLevelColor(level) {
      const option = this.riskLevelOptions.find(opt => opt.value === level)
      return option ? option.tagColor : '#909399'
    },

    // 获取变浅的标签背景色
    getLightenedColor(level) {
      const hexColor = this.getRiskLevelColor(level).replace(/^#/, "")
      const r = parseInt(hexColor.substring(0, 2), 16)
      const g = parseInt(hexColor.substring(2, 4), 16)
      const b = parseInt(hexColor.substring(4, 6), 16)

      // 计算变浅后的RGB值（90%变浅）
      const percent = 90
      const newR = Math.round(r + (255 - r) * (percent / 100))
      const newG = Math.round(g + (255 - g) * (percent / 100))
      const newB = Math.round(b + (255 - b) * (percent / 100))

      // 将RGB转换回16进制
      const toHex = (value) => {
        const hex = value.toString(16)
        return hex.length === 1 ? "0" + hex : hex
      }

      return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`
    },

    // 获取风险等级标签
    getRiskLevelLabel(level) {
      const option = this.riskLevelOptions.find(opt => opt.value === level)
      return option ? option.label : '未知'
    },

    // 获取状态颜色
    getStateColor(state) {
      if (state === 'valid') {
        return '#4584FF' // 有效状态为蓝色
      } else if (state === 'invalid') {
        return '#F56C6C' // 无效状态为红色
      }
      return '#909399' // 默认颜色
    },

    // 获取风险评分等级样式
    getRiskLevelClass(score) {
      score = score || 0
      if (score >= 80) return 'high-risk'
      if (score >= 60) return 'medium-risk'
      return 'low-risk'
    },

    // 检查是否有特定关联
    hasRelation(relationKey) {
      return this.enterpriseData[relationKey] === true
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 打印报告
    printReport() {
      window.print()
    },

    // 导出报告
    exportReport() {
      this.$message.success('报告导出功能开发中...')
    },

    // 切换企业基础信息模块展开/收起状态
    toggleEnterpriseBasic() {
      this.enterpriseBasicExpanded = !this.enterpriseBasicExpanded;
      // 注释掉自动收起子模块的逻辑，让用户自己控制
      // if (!this.enterpriseBasicExpanded) {
      //   this.legalPersonExpanded = false;
      //   this.responsiblePersonExpanded = false;
      // }
    },

    // 切换法人信息展开/收起状态
    toggleLegalPerson() {
      this.legalPersonExpanded = !this.legalPersonExpanded;
    },

    // 切换责任人信息展开/收起状态
    toggleResponsiblePerson() {
      this.responsiblePersonExpanded = !this.responsiblePersonExpanded;
    },

    // 初始化网络安全雷达图
    initSecurityRadarChart() {
      // 获取DOM元素
      const chartDom = document.getElementById('securityRadarChart');
      if (!chartDom) return;

      // 初始化echarts实例
      this.securityChart = echarts.init(chartDom);

      // 准备雷达图数据
      const radarData = this.securityAssessment.radarData;
      const indicator = radarData.map(item => ({
        name: item.name,
        max: 5 // 设置最大值为5
      }));

      const seriesData = [{
        value: radarData.map(item => item.value),
        name: '网络安全评分',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 215, 0, 0.6)' },
            { offset: 1, color: 'rgba(255, 215, 0, 0.1)' }
          ])
        }
      }];

      // 配置项
      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          textStyle: {
            color: '#fff'
          }
        },
        radar: {
          indicator: indicator,
          radius: '65%',
          splitNumber: 5,
          axisName: {
            color: '#fff',
            fontSize: 12,
            fontWeight: 'bold'
          },
          splitArea: {
            areaStyle: {
              color: ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)', 'rgba(255, 255, 255, 0.02)', 'rgba(255, 255, 255, 0.01)'],
              shadowColor: 'rgba(255, 255, 255, 0.1)',
              shadowBlur: 10
            }
          },
          axisLine: {
            lineStyle: {
              color: '#fff',
              width: 2
            }
          },
          splitLine: {
            lineStyle: {
              color: '#fff',
              width: 1,
              opacity: 0.8
            }
          }
        },
        series: [{
          type: 'radar',
          data: seriesData,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: '#FFD700'
          },
          itemStyle: {
            color: '#FFD700',
            borderColor: '#fff',
            borderWidth: 2
          },
          emphasis: {
            lineStyle: {
              width: 4,
              color: '#FFF700'
            },
            itemStyle: {
              color: '#FFF700',
              borderColor: '#fff',
              borderWidth: 3
            }
          }
        }]
      };

      // 设置配置项并渲染图表
      this.securityChart.setOption(option);

      // 监听窗口大小变化，调整图表大小
      window.addEventListener('resize', () => {
        this.securityChart && this.securityChart.resize();
      });
    },

    // 获取风险等级对应的颜色
    getSecurityRiskColor(riskLevel) {
      switch(riskLevel) {
        case '高风险': return '#F56C6C';
        case '中风险': return '#E6A23C';
        case '低风险': return '#60FB66';
        default: return '#909399';
      }
    },

    // 获取变化趋势的图标和颜色
    getChangeIcon(change) {
      if (change > 0) {
        return { icon: 'el-icon-top', color: '#67C23A' };
      } else if (change < 0) {
        return { icon: 'el-icon-bottom', color: '#F56C6C' };
      } else {
        return { icon: 'el-icon-minus', color: '#909399' };
      }
    },

    // 获取状态文本的颜色
    getStatusColor(status) {
      if (status === '通过' || status === '正常运营') {
        return '#10D205'; // 绿色
      } else {
        return '#F56C6C'; // 红色
      }
    },

    // 查看电子屏详情
    viewScreenDetail(row) {
      this.$message.success(`查看电子屏详情：${row.name}`);
      // 实际项目中可以跳转到详情页或打开详情对话框
    },

    // 修改电子屏信息
    editScreen(row) {
      this.$message.success(`修改电子屏信息：${row.name}`);
      // 实际项目中可以跳转到编辑页或打开编辑对话框
    },

    // 删除电子屏
    deleteScreen(row) {
      this.$confirm(`确认删除电子屏 "${row.name}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实际项目中应该调用删除API
        this.$message.success(`删除电子屏成功：${row.name}`);
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 初始化轮播容器
    initCarouselContainers() {
      this.$nextTick(() => {
        // 专门为IP地址段的轮播容器添加active类
        const ipCarousel = this.$refs.ipCarousel;

        if (ipCarousel) {
          const items = ipCarousel.querySelectorAll('li');

          if (items.length >= 2) {
            ipCarousel.classList.add('carousel-active');

            // 确保初始状态正确
            this.currentSlide = 0;

            // 初始化轮播位置
            this.updateSlidePosition();
          }
        }

        // 处理其他普通的轮播容器
        const otherCarousels = document.querySelectorAll('.carousel-container:not([ref="ipCarousel"])');
        otherCarousels.forEach(container => {
          const items = container.querySelectorAll('li');
          if (items.length >= 2) {
            container.classList.add('carousel-active');
          } else {
            container.classList.remove('carousel-active');
          }
        });
      });
    },

    // 开始自动轮播
    startAutoSlide() {
      this.autoSlideTimer = setInterval(() => {
        this.nextSlide();
      }, 6000); // 每6秒切换一次
    },

    // 下一张幻灯片
    nextSlide() {
      this.currentSlide = (this.currentSlide + 1) % 2;
      this.updateSlidePosition();
    },

    // 跳转到指定幻灯片
    goToSlide(index) {
      this.currentSlide = index;
      this.updateSlidePosition();
      // 重置自动轮播定时器
      // if (this.autoSlideTimer) {
      //   clearInterval(this.autoSlideTimer);
      //   this.startAutoSlide();
      // }
    },

    // 更新幻灯片位置
    updateSlidePosition() {
      this.$nextTick(() => {
        const carousel = this.$refs.ipCarousel;
        if (carousel) {
          const translateX = -this.currentSlide * 50; // 每次移动50%，因为每个li占50%宽度
          carousel.style.transform = `translateX(${translateX}%)`;
        }
      });
    },

    // 切换企业业务信息模块展开/收起状态
    toggleBusinessInfoExpanded() {
      this.businessInfoExpanded = !this.businessInfoExpanded
    },

    // 初始化滚动监听
    initScrollListener() {
      window.addEventListener('scroll', this.handleScroll, { passive: true })
    },

    // 处理滚动事件
    handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop

      // 遍历锚点列表，找到当前可见的锚点
      for (let i = this.anchorList.length - 1; i >= 0; i--) {
        const anchor = this.anchorList[i]
        const element = document.getElementById(anchor.id)

        if (element) {
          const elementTop = element.offsetTop - 100 // 偏移量，提前激活

          if (scrollTop >= elementTop) {
            this.currentActiveAnchor = anchor.id
            break
          }
        }
      }
    },

    // 滚动到指定锚点
    scrollToAnchor(anchorId) {
      const element = document.getElementById(anchorId)
      if (element) {
        const offsetTop = element.offsetTop - 80 // 留出一些顶部空间
        window.scrollTo({
          top: offsetTop,
          behavior: 'smooth'
        })
        this.currentActiveAnchor = anchorId
      }
    },

    // 切换锚点导航显示状态
    toggleAnchorNav() {
      this.showAnchorNav = !this.showAnchorNav
    },

    // 隐藏锚点导航
    hideAnchorNav() {
      this.showAnchorNav = false
    },

    // 初始化锚点导航位置
    initAnchorNavPosition() {
      // 设置初始位置为屏幕右侧中央
      this.anchorNavPosition.y = window.innerHeight / 2 - 200 // 减去导航高度的一半
      this.anchorNavPosition.x = 0 // 贴右边
    },

    // 开始拖拽
    startDrag(e) {
      this.isDragging = true
      this.dragStartPos.x = e.clientX
      this.dragStartPos.y = e.clientY

      document.addEventListener('mousemove', this.handleDrag)
      document.addEventListener('mouseup', this.stopDrag)

      // 阻止默认行为和事件冒泡
      e.preventDefault()
      e.stopPropagation()
    },

    // 处理拖拽
    handleDrag(e) {
      if (!this.isDragging) return

      const deltaX = this.dragStartPos.x - e.clientX
      const deltaY = e.clientY - this.dragStartPos.y

      // 计算新位置
      let newX = this.anchorNavPosition.x + deltaX
      let newY = this.anchorNavPosition.y + deltaY

      // 边界限制
      const navWidth = 200 // 导航宽度
      const navHeight = 400 // 导航最大高度

      // 限制在屏幕范围内
      newX = Math.max(0, Math.min(newX, window.innerWidth - navWidth))
      newY = Math.max(0, Math.min(newY, window.innerHeight - navHeight))

      // 更新位置
      this.anchorNavPosition.x = newX
      this.anchorNavPosition.y = newY

      // 更新拖拽起始位置
      this.dragStartPos.x = e.clientX
      this.dragStartPos.y = e.clientY
    },

    // 停止拖拽
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.handleDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    }
  }
}
</script>

<style lang="scss" scoped>
.enterprise-detail-report {
  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;

      .el-button {
        margin-right: 10px;
      }

      .report-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
      }
    }

    .header-right {
      .el-button {
        margin-left: 10px;
      }
    }
  }

  .report-content {
    background-color: #fff;

    // 可折叠标题样式
    .collapsible-title {
      transition: color 0.3s ease;

      &:hover {
        color: #409EFF;
      }

      i {
        transition: transform 0.3s ease;
      }
    }

    .enterprise-overview {
      padding: 20px;
      margin-bottom: 20px;
      background-color: #f5f7fa;
      border-radius: 4px;

      .overview-left {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .enterprise-photo {
          width: 100%;
          height: 150px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #fff;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          overflow: hidden;

          img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }

          i {
            font-size: 60px;
            color: #909399;
          }
        }
      }

      .overview-middle {
        .enterprise-header {
          display: flex;
          align-items: center;
          margin-bottom: 15px;

          .enterprise-name {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            margin-right: 20px;
          }

          .credit-code {
          font-size: 20px;
           color: #4584FF;

          }
        }

        .enterprise-tags {
          display: flex;
          flex-wrap: wrap;
          margin-bottom: 15px;
          padding-bottom: 15px;
          border-bottom: 1px solid #ebeef5;

          .risk-tag {
            margin-right: 8px;
            margin-bottom: 5px;
          }
        }

        .enterprise-info {
          .info-item {
            margin-bottom: 10px;
          }
        }
      }

      .overview-right {
        height: 100%;

        .license-photo {
          height: 100%;
          display: flex;
          flex-direction: column;
          background-color: #fff;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          overflow: hidden;

          .photo-title {
            padding: 8px;
            text-align: center;
            font-weight: 500;
            background-color: #f5f7fa;
            border-bottom: 1px solid #ebeef5;
          }

          .photo-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            position: relative;
            width: 100%;

            // 设置16:9的宽高比
            &:before {
              content: "";
              display: block;
              padding-top: 56.25%; // 16:9 = 9/16 = 0.5625 = 56.25%
            }

            img {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: contain;
              padding: 10px;
            }

            i {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size: 60px;
              color: #909399;
            }
          }
        }
      }
    }

    .detail-section {
      margin-bottom: 30px;

      .section-title {
        margin-top: 0;
        margin-bottom: 20px;
        padding-bottom: 10px;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        border-bottom: 1px solid #ebeef5;
      }

      // 企业基础信息模块样式
      &.enterprise-basic-section {
        border: 1px solid #ebeef5;
        border-radius: 4px;
        overflow: hidden;

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px 20px;
          background-color: #f5f7fa;
          cursor: pointer;
          border-bottom: none;

          &:hover {
            background-color: #eef1f6;
          }

          .section-title {
            margin: 0;
            padding: 0;
            border: none;
            font-size: 18px;
            font-weight: 600;
          }

          .section-actions {
            display: flex;
            align-items: center;

            .count-badge {
              display: inline-block;
              padding: 2px 8px;
              margin-right: 10px;
              background-color: #409EFF;
              color: #fff;
              border-radius: 10px;
              font-size: 12px;
            }

            i {
              font-size: 16px;
              color: #909399;
            }
          }
        }

        .section-content {
          padding: 20px;
          background-color: #fff;
        }
      }

      .subsection-title {
        margin-top: 20px;
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }

      .subsection {
        margin-top: 25px;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        overflow: hidden;

        .subsection-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 15px;
          background-color: #f5f7fa;
          cursor: pointer;

          &:hover {
            background-color: #eef1f6;
          }

          .subsection-title {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
          }

          .subsection-actions {
            display: flex;
            align-items: center;

            .count-badge {
              display: inline-block;
              padding: 2px 8px;
              margin-right: 10px;
              background-color: #409EFF;
              color: #fff;
              border-radius: 10px;
              font-size: 12px;
            }

            i {
              font-size: 16px;
              color: #909399;
            }
          }
        }

        .subsection-content {
          padding: 15px;
          background-color: #fff;

          .person-item {
            margin-bottom: 15px;

            &:last-child {
              margin-bottom: 0;
            }

            .person-header {
              margin-bottom: 15px;

              .person-title {
                margin: 0;
                font-size: 15px;
                font-weight: 500;
                color: #409EFF;
              }
            }

            .person-info {
              margin-bottom: 15px;
            }

            .person-photos {
              margin-top: 20px;

              .photo-item {
                height: 100%;
                display: flex;
                flex-direction: column;
                background-color: #fff;
                border: 1px solid #ebeef5;
                border-radius: 4px;
                overflow: hidden;

                .photo-title {
                  padding: 8px;
                  text-align: center;
                  font-weight: 500;
                  background-color: #f5f7fa;
                  border-bottom: 1px solid #ebeef5;
                }

                .photo-container {
                  flex: 1;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  position: relative;
                  width: 100%;

                  // 设置16:9的宽高比
                  &:before {
                    content: "";
                    display: block;
                    padding-top: 56.25%; // 16:9 = 9/16 = 0.5625 = 56.25%
                  }

                  img {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    padding: 10px;
                  }

                  i {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 60px;
                    color: #909399;
                  }
                }
              }
            }
          }
        }
      }

      .info-item {
        margin-bottom: 15px;

        .label {
          color: #909399;
          margin-right: 5px;
        }

        .value {
          color: #303133;
          word-break: break-all;
        }
      }

      .risk-chart {
        margin: 20px 0;

        .chart-placeholder {
          padding: 20px;
          background-color: #fff;
          border-radius: 4px;

          .risk-score {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .score-circle {
              width: 150px;
              height: 150px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 15px;

              &.high-risk {
                background-color: rgba(245, 108, 108, 0.2);
                border: 4px solid #F56C6C;

                .score-value {
                  color: #F56C6C;
                }
              }

              &.medium-risk {
                background-color: rgba(230, 162, 60, 0.2);
                border: 4px solid #E6A23C;

                .score-value {
                  color: #E6A23C;
                }
              }

              &.low-risk {
                background-color: rgba(103, 194, 58, 0.2);
                border: 4px solid #67C23A;

                .score-value {
                  color: #67C23A;
                }
              }

              .score-value {
                font-size: 36px;
                font-weight: bold;
              }
            }

            .score-label {
              font-size: 16px;
              color: #606266;
            }
          }

          .risk-distribution {
            h4 {
              margin-top: 0;
              margin-bottom: 20px;
              font-size: 16px;
              color: #303133;
            }

            .risk-bars {
              .risk-bar-item {
                display: flex;
                align-items: center;
                margin-bottom: 15px;

                .bar-label {
                  width: 80px;
                  text-align: right;
                  margin-right: 10px;
                  color: #606266;
                }

                .bar-container {
                  flex: 1;
                  height: 12px;
                  background-color: #ebeef5;
                  border-radius: 6px;
                  overflow: hidden;

                  .bar-value {
                    height: 100%;
                    border-radius: 6px;
                  }
                }

                .bar-percentage {
                  width: 50px;
                  text-align: right;
                  margin-left: 10px;
                  color: #606266;
                }
              }
            }
          }
        }
      }

      .record-result {
        margin-top: 8px;
        font-size: 13px;

        .label {
          color: #909399;
        }

        .value {
          color: #303133;
        }
      }
    }
  }
}

// 备案信息卡片样式
.record-cards-section {
  margin-bottom: 20px;

  .record-card {
    height: 100%;
    background-color: #F6F7FB;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #EBEEF5;
    }

    .card-content {
      .card-item {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .item-label {
          color: #606266;
          font-size: 14px;
          margin-right: 5px;
        }

        .item-value {
          color: #303133;
          font-weight: 500;
          font-size: 14px;
        }
      }
    }
  }
}

// 关联电子屏样式
.electronic-screens-section {
  .section-title {
    display: flex;
    align-items: center;

    .count-badge {
      display: inline-block;
      margin-left: 10px;
      font-size: 14px;
      color: #606266;
      font-weight: normal;
    }
  }

  .electronic-screens-table {
    margin-top: 15px;

    .el-table {
      .el-button {
        padding: 5px 8px;
        font-size: 12px;
      }

      .el-button + .el-button {
        margin-left: 5px;
      }

      // 设置表头背景色
      ::v-deep  .el-table__header-wrapper {
        th {
          background-color: #EBF1FF !important;
        }
      }

      // 标签样式优化
      .el-tag {
        margin-bottom: 4px;
      }
    }
  }
}

// 网络安全评估样式
.security-assessment-section {
  .security-score-panel {
    // background-color: #fff;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    text-align: center;
    height: 350px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .score-title {
      font-size: 16px;
      color: #fff;
      margin-bottom: 15px;
    }

    .score-value {
      font-size: 48px;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .score-change {
      margin-bottom: 10px;
      font-size: 14px;
      color: #fff;

      i {
        margin: 0 5px;
      }
    }

    .score-compare {
      margin-bottom: 15px;
      font-size: 14px;
      color: #fff;

      span {
        color: #409EFF;
        font-weight: bold;
      }
    }

    .score-stars {
      margin-bottom: 15px;
      font-size: 20px;

      i {
        margin: 0 2px;
      }
    }

    .risk-level {
      font-size: 18px;
      font-weight: bold;
    }
  }

  .security-radar-chart {
    // background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    height: 350px;
  }

  .security-assessment-details {
    // background-color: #fff;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    height: 350px;
    overflow-y: auto;
    p{
      font-size: 18px;
    }
    .assessment-overview {
      margin-bottom: 20px;
      font-size: 18px;

      h4 {
        font-size: 16px;
        color: #fff;
        margin-top: 0;
        margin-bottom: 10px;
        font-weight: 500;
      }

      p {
        color: #fff;
        line-height: 1.6;
        margin: 0;
      }
    }

    .assessment-items {
      .assessment-item {
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        .item-title {
          font-weight: 500;
          color: #fff;
          margin-bottom: 5px;
        }

        .item-content {
          color: #fff;
          line-height: 1.6;
          
          .item-score {
            font-weight: bold;
            color: #fff;
            margin: 0 3px;
          }

          .item-status {
            font-weight: 500;
            margin-left: 3px;

            &.status-good {
              color: #67C23A;
            }

            &.status-warning {
              color: #E6A23C;
            }

            &.status-danger {
              color: #F56C6C;
            }
          }
        }
      }
    }
  }
}

// 历史合规记录样式
.compliance-records-section {
  .compliance-card {
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #ebeef5;

    .card-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      height: 100%;

      .card-left {
        flex: 1;
        margin-right: 20px;

        .record-type {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 15px;
          line-height: 1.4;
        }

        .record-stats {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .total-count {
            font-size: 28px;
            font-weight: bold;
            color: #409EFF;
          }

          .recent-period {
            background-color: #f0f9ff;
            color: #409EFF;
            border-color: #409EFF;
          }
        }
      }

      .card-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;

        .check-time {
          font-size: 14px;
          color: #606266;
          margin-bottom: 15px;
        }

        .problem-stats {
          .problem-count {
            font-size: 14px;
            color: #303133;
            margin-bottom: 10px;
            display: block;
          }

          .resolution-status {
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .status-label {
              font-size: 14px;
              color: #606266;
              margin-right: 8px;
              white-space: nowrap;
            }

            .status-tag {
              margin-right: 5px;
              margin-bottom: 5px;

              &.el-tag--danger {
                background-color: #fef0f0;
                border-color: #fbc4c4;
                color: #f56c6c;
              }

              &.el-tag--success {
                background-color: #f0f9ff;
                border-color: #b3d8ff;
                color: #67c23a;
              }
            }
          }
        }

        // 行政处罚和行政处置样式
        .action-time {
          font-size: 14px;
          color: #606266;
          margin-bottom: 15px;
        }

        .action-details {
          .action-reason {
            font-size: 14px;
            color: #303133;
            margin-bottom: 8px;
            line-height: 1.4;
          }

          .action-result {
            font-size: 14px;
            color: #E6A23C;
            font-weight: 500;
            line-height: 1.4;
          }
        }
      }
    }

    &:hover {
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }
  }

  // 历史合规记录详细列表样式
  .compliance-detail-list {
    margin-top: 30px;

    .el-table {
      border-radius: 8px;
      overflow: hidden;

      .el-table__header-wrapper {
        .el-table__header {
          th {
            background-color: #EBF1FF !important;
            color: #303133 !important;
            font-weight: 600;
            font-size: 14px;
            padding: 15px 0;
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__body {
          tr {
            &:hover {
              background-color: #f5f7fa;
            }

            td {
              padding: 20px 12px;
              border-bottom: 1px solid #ebeef5;

              .case-description {
                line-height: 1.6;
                color: #606266;
                font-size: 14px;
                text-align: justify;
                word-break: break-word;
                white-space: pre-wrap;
              }
            }
          }
        }
      }

      // 表格边框样式
      .el-table--border {
        border: 1px solid #ebeef5;

        &::after {
          background-color: #ebeef5;
        }

        &::before {
          background-color: #ebeef5;
        }
      }

      // 表格单元格边框
      .el-table td, .el-table th {
        border-right: 1px solid #ebeef5;
      }

      // 最后一列不显示右边框
      .el-table td:last-child, .el-table th:last-child {
        border-right: none;
      }
    }
  }
}

// 当前数字资产清单样式
.digital-asset-section {
  .asset-cards-container {
    display: grid;
    grid-template-columns: repeat(9, 1fr);
    gap: 15px;
    margin-top: 20px;

    .asset-card {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 20px 15px;
      border: 1px solid;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      min-height: 100px;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
      }

      .asset-name {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 10px;
        text-align: center;
        line-height: 1.3;
      }

      .asset-count {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        line-height: 1;
      }

      // 添加微妙的渐变效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0) 50%);
        pointer-events: none;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .asset-cards-container {
      grid-template-columns: repeat(5, 1fr);
      gap: 12px;

      .asset-card {
        padding: 18px 12px;
        min-height: 95px;

        .asset-name {
          font-size: 13px;
          margin-bottom: 8px;
        }

        .asset-count {
          font-size: 22px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .asset-cards-container {
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;

      .asset-card {
        padding: 16px 10px;
        min-height: 85px;

        .asset-name {
          font-size: 12px;
          margin-bottom: 6px;
        }

        .asset-count {
          font-size: 20px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .asset-cards-container {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;

      .asset-card {
        padding: 14px 8px;
        min-height: 75px;

        .asset-name {
          font-size: 11px;
          margin-bottom: 5px;
        }

        .asset-count {
          font-size: 18px;
        }
      }
    }
  }

  // asset-cards-detail 容器样式
  .asset-cards-detail {
    margin-top: 30px;

    > ul {
      display: flex;
      gap: 20px;
      list-style: none;
      padding: 0;
      margin: 0;

      .detail-item {
        flex: 1;
        background-color: #F6F7FB;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

        .cards-detail-tit {
          margin: 0 0 15px 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          border-bottom: 1px solid #E4E7ED;
          padding-bottom: 10px;
        }

        .carousel-wrapper {
          position: relative;
          overflow: hidden; // 在wrapper层设置overflow

          .carousel-container {
            list-style: none;
            padding: 0;
            margin: 0;
            position: relative;

            // 默认情况下，非轮播模式
            &:not(.carousel-active) {
              li {
                // 如果有label-fields-container，使用网格布局
                .label-fields-container {
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  gap: 15px 20px;

                  .label-field {
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    margin-bottom: 0;

                    .left-lable {
                      min-width: 80px;
                      color: #606266;
                      font-weight: 500;
                      margin-right: 8px;
                      white-space: nowrap;
                    }

                    span {
                      color: #303133;
                      flex: 1;
                    }
                  }
                }

                // 如果没有label-fields-container，使用原来的样式
                .label-field:not(.label-fields-container .label-field) {
                  display: flex;
                  align-items: center;
                  margin-bottom: 12px;
                  font-size: 14px;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .left-lable {
                    min-width: 100px;
                    color: #606266;
                    font-weight: 500;
                    margin-right: 10px;
                  }

                  span {
                    color: #303133;
                    flex: 1;
                  }
                }
              }
            }

            // 轮播模式样式 - 使用更高的优先级
            &.carousel-active {
              display: flex !important;
              transition: transform 0.5s ease-in-out;
              height: auto;
              min-height: 120px;
              width: 200%; // 设置容器宽度为200%以容纳两个li

              li {
                min-width: 50% !important; // 每个li占50%宽度
                flex-shrink: 0;
                box-sizing: border-box;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;

                // 隐藏默认的label-field样式
                .label-field {
                  display: none !important;
                }

                .label-fields-container {
                  display: grid !important;
                  grid-template-columns: 1fr 1fr;
                  gap: 15px 20px;
                  width: 100%;
                  padding: 0 10px; // 添加一些内边距

                  .label-field {
                    display: flex !important;
                    align-items: center;
                    font-size: 14px;
                    margin-bottom: 0;

                    .left-lable {
                      min-width: 80px;
                      color: #606266;
                      font-weight: 500;
                      margin-right: 8px;
                      white-space: nowrap;
                    }

                    span {
                      color: #303133;
                      flex: 1;
                    }
                  }
                }
              }
            }
          }

          .carousel-indicators {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            gap: 8px;

            .indicator {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #dcdfe6;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background-color: #c0c4cc;
              }

              &.active {
                background-color: #409EFF;
                transform: scale(1.2);
              }
            }
          }
        }
      }
    }

    // 响应式设计
    @media (max-width: 768px) {
      > ul {
        flex-direction: column;
        gap: 15px;

        .detail-item {
          padding: 15px;

          .cards-detail-tit {
            font-size: 14px;
            margin-bottom: 12px;
          }

          .carousel-wrapper {
            .carousel-container {
              // 非轮播模式的移动端样式
              &:not(.carousel-active) li {
                .label-fields-container {
                  grid-template-columns: 1fr;
                  gap: 10px;

                  .label-field {
                    font-size: 13px;

                    .left-lable {
                      min-width: 70px;
                      font-size: 13px;
                    }
                  }
                }

                .label-field:not(.label-fields-container .label-field) {
                  font-size: 13px;
                  margin-bottom: 10px;

                  .left-lable {
                    min-width: 70px;
                    font-size: 13px;
                  }
                }
              }

              // 轮播模式的移动端样式
              &.carousel-active li {
                .label-fields-container {
                  grid-template-columns: 1fr;
                  gap: 10px;

                  .label-field {
                    font-size: 13px;

                    .left-lable {
                      min-width: 70px;
                      font-size: 13px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// 锚点导航样式
.anchor-navigation {
  position: fixed;
  z-index: 1000;
  display: flex;
  align-items: center;
  transition: none; // 拖拽时不需要过渡动画

  .anchor-nav-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 8px 0 0 8px;
    box-shadow: -2px 0 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #ebeef5;
    border-right: none;
    overflow: hidden;
    transition: transform 0.3s ease;
    max-width: 200px;

    .anchor-nav-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
      cursor: move;
      user-select: none;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: #eef1f6;
      }

      &.dragging {
        background-color: #409EFF;
        color: #fff;

        .nav-title {
          color: #fff;
        }

        .el-icon-close {
          color: #fff;

          &:hover {
            color: #f0f2f5;
          }
        }
      }

      .nav-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        transition: color 0.3s ease;
      }

      .el-icon-close {
        font-size: 16px;
        color: #909399;
        cursor: pointer;
        padding: 2px;
        transition: color 0.3s ease;

        &:hover {
          color: #606266;
        }
      }
    }

    .anchor-nav-list {
      list-style: none;
      padding: 0;
      margin: 0;
      max-height: 400px;
      overflow-y: auto;

      .anchor-nav-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 1px solid #f0f2f5;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: #f5f7fa;
          color: #409EFF;
        }

        &.active {
          background-color: #409EFF;
          color: #fff;

          i {
            color: #fff;
          }
        }

        i {
          font-size: 16px;
          margin-right: 8px;
          color: #909399;
          transition: color 0.3s ease;
        }

        .anchor-text {
          font-size: 13px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .anchor-nav-toggle {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid #ebeef5;
    border-radius: 8px 0 0 8px;
    border-right: none;
    padding: 12px 8px;
    cursor: pointer;
    box-shadow: -2px 0 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
    }

    i {
      font-size: 16px;
      color: #606266;
      transition: color 0.3s ease;
    }
  }

  // 隐藏状态
  &:not([v-show="true"]) .anchor-nav-container {
    transform: translateX(100%);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .anchor-navigation {
    .anchor-nav-container {
      max-width: 160px;

      .anchor-nav-header {
        padding: 10px 12px;

        .nav-title {
          font-size: 13px;
        }
      }

      .anchor-nav-list {
        .anchor-nav-item {
          padding: 10px 12px;

          i {
            font-size: 14px;
            margin-right: 6px;
          }

          .anchor-text {
            font-size: 12px;
          }
        }
      }
    }

    .anchor-nav-toggle {
      padding: 10px 6px;

      i {
        font-size: 14px;
      }
    }
  }
}

@media print {
  .report-header, .el-tabs__header, .anchor-navigation {
    display: none !important;
  }

  .el-tabs__content {
    margin-top: 20px;
  }

  .app-container {
    padding: 0 !important;
  }
}
</style>


