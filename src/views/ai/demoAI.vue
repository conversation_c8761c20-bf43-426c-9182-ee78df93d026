<template>
  <div class="demo-ai-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <i class="el-icon-chat-dot-round"></i>
            AI智能助手演示
          </h1>
          <p class="page-description">
            基于maxKB构建的智能问答系统，支持自然语言对话、知识库检索和智能推理
          </p>
        </div>
        <div class="header-right">
          <el-button-group>
            <el-button 
              :type="chatMode === 'normal' ? 'primary' : 'default'"
              @click="setChatMode('normal')"
              icon="el-icon-chat-line-round"
            >
              普通对话
            </el-button>
            <el-button 
              :type="chatMode === 'knowledge' ? 'primary' : 'default'"
              @click="setChatMode('knowledge')"
              icon="el-icon-collection"
            >
              知识库问答
            </el-button>
            <el-button 
              :type="chatMode === 'analysis' ? 'primary' : 'default'"
              @click="setChatMode('analysis')"
              icon="el-icon-data-analysis"
            >
              数据分析
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" :class="{ 'fullscreen': isFullscreen }">
      <div class="content-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" v-show="!isFullscreen">
          <!-- 新建对话按钮 -->
          <div class="sidebar-section">
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="createNewConversation"
              class="new-chat-btn"
              :loading="isCreatingChat"
            >
              新建对话
            </el-button>
          </div>

          <!-- 历史记录 -->
          <div class="sidebar-section">
            <h3 class="section-title">
              <i class="el-icon-time"></i>
              历史记录
              <el-button
                type="text"
                size="mini"
                icon="el-icon-refresh"
                @click="refreshHistory"
                class="refresh-btn"
                :loading="isRefreshingHistory"
              ></el-button>
            </h3>

            <!-- 搜索框 -->
            <div class="search-box">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索对话..."
                size="small"
                prefix-icon="el-icon-search"
                clearable
                @input="filterHistory"
              ></el-input>
            </div>

            <!-- 历史记录列表 -->
            <div class="history-list">
              <div v-if="filteredHistory.length === 0" class="empty-history">
                <i class="el-icon-chat-line-round"></i>
                <p>{{ searchKeyword ? '未找到相关对话' : '暂无历史记录' }}</p>
              </div>

              <div
                v-for="conversation in filteredHistory"
                :key="conversation.id"
                :class="['history-item', { 'active': currentConversationId === conversation.id }]"
                @click="loadConversation(conversation)"
              >
                <div class="conversation-info">
                  <div class="conversation-title">
                    {{ conversation.title || '新对话' }}
                  </div>
                  <div class="conversation-preview">
                    {{ conversation.lastMessage || '开始新的对话...' }}
                  </div>
                  <div class="conversation-time">
                    {{ formatTime(conversation.updatedAt) }}
                  </div>
                </div>
                <div class="conversation-actions">
                  <el-dropdown trigger="click" @command="handleConversationAction">
                    <el-button type="text" size="mini" icon="el-icon-more"></el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="{action: 'rename', id: conversation.id}">
                        <i class="el-icon-edit"></i> 重命名
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'delete', id: conversation.id}" divided>
                        <i class="el-icon-delete"></i> 删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area">
          <max-kb-chat
            :title="currentChatTitle"
            :welcome-title="currentWelcomeTitle"
            :welcome-text="currentWelcomeText"
            :placeholder="currentPlaceholder"
            :quick-questions="currentQuickQuestions"
            :use-stream="useStream"
            :max-messages="maxMessages"
            @fullscreen-change="handleFullscreenChange"
            ref="chatComponent"
          />
        </div>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar" v-show="!isFullscreen">
      <div class="status-left">
        <span class="status-item">
          <i class="el-icon-connection"></i>
          {{ connectionStatus }}
        </span>
        <span class="status-item">
          <i class="el-icon-time"></i>
          {{ currentTime }}
        </span>
      </div>
      <div class="status-right">
        <span class="status-item">
          模式: {{ chatModeText }}
        </span>
        <span class="status-item">
          消息: {{ messageCount }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import MaxKBChat from '@/components/MaxKBChat.vue'
import { getApplicationInfo } from '@/api/maxkb'

export default {
  name: 'DemoAI',
  components: {
    MaxKBChat
  },
  data() {
    return {
      chatMode: 'normal', // normal, knowledge, analysis
      isFullscreen: false,
      useStream: true,
      maxMessages: 100,
      messageCount: 0,
      connectionStatus: '已连接',
      currentTime: '',
      timeInterval: null,

      // 历史记录相关
      conversations: [], // 所有对话历史
      filteredHistory: [], // 过滤后的历史记录
      currentConversationId: null, // 当前对话ID
      searchKeyword: '', // 搜索关键词
      isCreatingChat: false, // 是否正在创建新对话
      isRefreshingHistory: false, // 是否正在刷新历史记录

      // 不同模式的配置
      chatConfigs: {
        normal: {
          title: 'AI智能助手',
          welcomeTitle: '欢迎使用AI智能助手',
          welcomeText: '我是您的智能助手，可以回答各种问题，协助您完成工作。请输入您的问题开始对话。',
          placeholder: '请输入您的问题...',
          quickQuestions: [
            '你好，请介绍一下自己',
            '你能帮我做什么？',
            '如何使用这个系统？',
            '请推荐一些学习资源'
          ]
        },
        knowledge: {
          title: '知识库问答',
          welcomeTitle: '知识库智能问答',
          welcomeText: '我可以基于知识库为您提供准确的信息检索和问答服务。请输入您想了解的内容。',
          placeholder: '请输入您想查询的知识...',
          quickQuestions: [
            '系统有哪些功能模块？',
            '如何进行企业管理？',
            '数据安全如何保障？',
            '系统支持哪些操作？'
          ]
        },
        analysis: {
          title: '数据分析助手',
          welcomeTitle: '智能数据分析',
          welcomeText: '我可以帮助您分析数据、生成报告和提供业务洞察。请描述您的分析需求。',
          placeholder: '请描述您的数据分析需求...',
          quickQuestions: [
            '分析企业风险分布情况',
            '生成月度数据报告',
            '对比不同区域的数据',
            '预测未来发展趋势'
          ]
        }
      }
    }
  },
  computed: {
    currentChatTitle() {
      return this.chatConfigs[this.chatMode].title
    },
    currentWelcomeTitle() {
      return this.chatConfigs[this.chatMode].welcomeTitle
    },
    currentWelcomeText() {
      return this.chatConfigs[this.chatMode].welcomeText
    },
    currentPlaceholder() {
      return this.chatConfigs[this.chatMode].placeholder
    },
    currentQuickQuestions() {
      return this.chatConfigs[this.chatMode].quickQuestions
    },
    chatModeText() {
      const modeMap = {
        normal: '普通对话',
        knowledge: '知识库问答',
        analysis: '数据分析'
      }
      return modeMap[this.chatMode]
    }
  },
  mounted() {
    this.initTime()
    this.checkConnection()
    this.loadConversationHistory()
    this.createNewConversation()
  },
  beforeDestroy() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    // 设置聊天模式
    setChatMode(mode) {
      if (this.chatMode !== mode) {
        this.chatMode = mode
        this.$message.success(`已切换到${this.chatModeText}模式`)

        // 清空当前对话
        if (this.$refs.chatComponent) {
          this.$refs.chatComponent.clearChat()
        }
      }
    },

    // 处理全屏变化
    handleFullscreenChange(isFullscreen) {
      this.isFullscreen = isFullscreen
    },

    // 初始化时间
    initTime() {
      this.updateTime()
      this.timeInterval = setInterval(this.updateTime, 1000)
    },

    // 更新时间
    updateTime() {
      this.currentTime = new Date().toLocaleTimeString('zh-CN')
    },

    // 检查连接状态
    async checkConnection() {
      try {
        await getApplicationInfo()
        this.connectionStatus = '已连接'
      } catch (error) {
        this.connectionStatus = '连接失败'
        console.error('连接maxKB失败:', error)
      }
    },

    // 创建新对话
    async createNewConversation() {
      this.isCreatingChat = true
      try {
        const newConversation = {
          id: this.generateId(),
          title: '',
          lastMessage: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          mode: this.chatMode,
          messages: []
        }

        this.conversations.unshift(newConversation)
        this.currentConversationId = newConversation.id
        this.saveConversationHistory()
        this.filterHistory()

        // 清空聊天组件
        if (this.$refs.chatComponent) {
          this.$refs.chatComponent.clearChat()
        }

        this.$message.success('新对话已创建')
      } catch (error) {
        this.$message.error('创建对话失败')
        console.error('创建对话失败:', error)
      } finally {
        this.isCreatingChat = false
      }
    },

    // 加载对话历史
    loadConversationHistory() {
      try {
        const saved = localStorage.getItem('ai_conversations')
        if (saved) {
          this.conversations = JSON.parse(saved).map(conv => ({
            ...conv,
            createdAt: new Date(conv.createdAt),
            updatedAt: new Date(conv.updatedAt)
          }))
        }
        this.filterHistory()
      } catch (error) {
        console.error('加载对话历史失败:', error)
        this.conversations = []
      }
    },

    // 保存对话历史
    saveConversationHistory() {
      try {
        localStorage.setItem('ai_conversations', JSON.stringify(this.conversations))
      } catch (error) {
        console.error('保存对话历史失败:', error)
      }
    },

    // 刷新历史记录
    async refreshHistory() {
      this.isRefreshingHistory = true
      try {
        // 模拟刷新延迟
        await new Promise(resolve => setTimeout(resolve, 500))
        this.loadConversationHistory()
        this.$message.success('历史记录已刷新')
      } catch (error) {
        this.$message.error('刷新失败')
      } finally {
        this.isRefreshingHistory = false
      }
    },

    // 过滤历史记录
    filterHistory() {
      if (!this.searchKeyword.trim()) {
        this.filteredHistory = [...this.conversations]
      } else {
        const keyword = this.searchKeyword.toLowerCase()
        this.filteredHistory = this.conversations.filter(conv =>
          (conv.title && conv.title.toLowerCase().includes(keyword)) ||
          (conv.lastMessage && conv.lastMessage.toLowerCase().includes(keyword))
        )
      }
    },

    // 加载对话
    loadConversation(conversation) {
      this.currentConversationId = conversation.id
      this.chatMode = conversation.mode || 'normal'

      // 加载对话消息到聊天组件
      if (this.$refs.chatComponent && conversation.messages) {
        this.$refs.chatComponent.messages = [...conversation.messages]
      }

      this.$message.success(`已加载对话: ${conversation.title || '新对话'}`)
    },

    // 处理对话操作
    handleConversationAction(command) {
      const { action, id } = command
      const conversation = this.conversations.find(conv => conv.id === id)

      if (!conversation) return

      switch (action) {
        case 'rename':
          this.renameConversation(conversation)
          break
        case 'delete':
          this.deleteConversation(conversation)
          break
      }
    },

    // 重命名对话
    renameConversation(conversation) {
      this.$prompt('请输入新的对话名称', '重命名对话', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: conversation.title || '新对话',
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '对话名称不能为空'
          }
          return true
        }
      }).then(({ value }) => {
        conversation.title = value.trim()
        conversation.updatedAt = new Date()
        this.saveConversationHistory()
        this.filterHistory()
        this.$message.success('重命名成功')
      }).catch(() => {})
    },

    // 删除对话
    deleteConversation(conversation) {
      this.$confirm(`确定要删除对话"${conversation.title || '新对话'}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.conversations.findIndex(conv => conv.id === conversation.id)
        if (index > -1) {
          this.conversations.splice(index, 1)

          // 如果删除的是当前对话，创建新对话
          if (this.currentConversationId === conversation.id) {
            this.createNewConversation()
          }

          this.saveConversationHistory()
          this.filterHistory()
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },

    // 格式化时间
    formatTime(date) {
      if (!date) return ''

      const now = new Date()
      const time = new Date(date)
      const diff = now - time

      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 24小时内
        return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else if (diff < 604800000) { // 7天内
        const days = Math.floor(diff / 86400000)
        return `${days}天前`
      } else {
        return time.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
      }
    },

    // 生成唯一ID
    generateId() {
      return 'conv_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-ai-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e1e8ed;
  padding: 20px 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;

    .header-left {
      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        color: #333;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #667eea;
        }
      }

      .page-description {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }
  }
}

.main-content {
  flex: 1;
  padding: 20px 24px;
  overflow: hidden;

  &.fullscreen {
    padding: 0;
  }

  .content-wrapper {
    display: flex;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
    gap: 20px;
  }
}

.sidebar {
  width: 280px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow-y: auto;

  .sidebar-section {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #333;
      display: flex;
      align-items: center;
      justify-content: space-between;

      i {
        margin-right: 6px;
        color: #667eea;
      }

      .refresh-btn {
        padding: 0;
        margin-left: auto;
        color: #999;

        &:hover {
          color: #667eea;
        }
      }
    }

    .new-chat-btn {
      width: 100%;
      height: 40px;
      font-size: 14px;
      border-radius: 8px;
    }

    .search-box {
      margin-bottom: 12px;
    }

    .history-list {
      max-height: 400px;
      overflow-y: auto;

      .empty-history {
        text-align: center;
        padding: 40px 20px;
        color: #999;

        i {
          font-size: 32px;
          margin-bottom: 8px;
          display: block;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }

      .history-item {
        display: flex;
        align-items: flex-start;
        padding: 12px;
        margin-bottom: 8px;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: #667eea;
          background: #f8f9ff;
        }

        &.active {
          border-color: #667eea;
          background: #f0f4ff;
        }

        .conversation-info {
          flex: 1;
          min-width: 0;

          .conversation-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .conversation-preview {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .conversation-time {
            font-size: 11px;
            color: #999;
          }
        }

        .conversation-actions {
          flex-shrink: 0;
          margin-left: 8px;
          opacity: 0;
          transition: opacity 0.2s ease;

          .el-button {
            padding: 4px;
            color: #999;

            &:hover {
              color: #667eea;
            }
          }
        }

        &:hover .conversation-actions {
          opacity: 1;
        }
      }
    }
  }
}

.chat-area {
  flex: 1;
  min-width: 0;
}

.status-bar {
  background: white;
  border-top: 1px solid #e1e8ed;
  padding: 8px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;

  .status-left,
  .status-right {
    display: flex;
    gap: 16px;
  }

  .status-item {
    display: flex;
    align-items: center;

    i {
      margin-right: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .sidebar {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px 16px;

    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;

      .header-left .page-title {
        font-size: 20px;
      }
    }
  }

  .main-content {
    padding: 16px;

    .content-wrapper {
      flex-direction: column;
      gap: 16px;
    }
  }

  .sidebar {
    width: 100%;
    order: 2;
  }

  .chat-area {
    order: 1;
  }

  .status-bar {
    padding: 8px 16px;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;

    .status-left,
    .status-right {
      gap: 12px;
    }
  }
}
</style>
