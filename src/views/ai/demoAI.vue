<template>
  <div class="demo-ai-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <i class="el-icon-chat-dot-round"></i>
            AI智能助手演示
          </h1>
          <p class="page-description">
            基于maxKB构建的智能问答系统，支持自然语言对话、知识库检索和智能推理
          </p>
        </div>
        <div class="header-right">
          <el-button-group>
            <el-button 
              :type="chatMode === 'normal' ? 'primary' : 'default'"
              @click="setChatMode('normal')"
              icon="el-icon-chat-line-round"
            >
              普通对话
            </el-button>
            <el-button 
              :type="chatMode === 'knowledge' ? 'primary' : 'default'"
              @click="setChatMode('knowledge')"
              icon="el-icon-collection"
            >
              知识库问答
            </el-button>
            <el-button 
              :type="chatMode === 'analysis' ? 'primary' : 'default'"
              @click="setChatMode('analysis')"
              icon="el-icon-data-analysis"
            >
              数据分析
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" :class="{ 'fullscreen': isFullscreen }">
      <div class="content-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" v-show="!isFullscreen">
          <div class="sidebar-section">
            <h3 class="section-title">
              <i class="el-icon-star-on"></i>
              功能特性
            </h3>
            <ul class="feature-list">
              <li>
                <i class="el-icon-check"></i>
                智能对话交互
              </li>
              <li>
                <i class="el-icon-check"></i>
                知识库检索
              </li>
              <li>
                <i class="el-icon-check"></i>
                流式响应
              </li>
              <li>
                <i class="el-icon-check"></i>
                上下文理解
              </li>
              <li>
                <i class="el-icon-check"></i>
                多轮对话
              </li>
              <li>
                <i class="el-icon-check"></i>
                Markdown支持
              </li>
            </ul>
          </div>

          <div class="sidebar-section">
            <h3 class="section-title">
              <i class="el-icon-question"></i>
              示例问题
            </h3>
            <div class="example-questions">
              <el-button
                v-for="(question, index) in currentExamples"
                :key="index"
                size="small"
                type="text"
                @click="askExample(question)"
                class="example-button"
              >
                {{ question }}
              </el-button>
            </div>
          </div>

          <div class="sidebar-section">
            <h3 class="section-title">
              <i class="el-icon-setting"></i>
              设置
            </h3>
            <div class="settings">
              <div class="setting-item">
                <label>流式响应</label>
                <el-switch v-model="useStream"></el-switch>
              </div>
              <div class="setting-item">
                <label>显示时间戳</label>
                <el-switch v-model="showTimestamp"></el-switch>
              </div>
              <div class="setting-item">
                <label>自动滚动</label>
                <el-switch v-model="autoScroll"></el-switch>
              </div>
            </div>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area">
          <max-kb-chat
            :title="currentChatTitle"
            :welcome-title="currentWelcomeTitle"
            :welcome-text="currentWelcomeText"
            :placeholder="currentPlaceholder"
            :quick-questions="currentQuickQuestions"
            :use-stream="useStream"
            :max-messages="maxMessages"
            @fullscreen-change="handleFullscreenChange"
            ref="chatComponent"
          />
        </div>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar" v-show="!isFullscreen">
      <div class="status-left">
        <span class="status-item">
          <i class="el-icon-connection"></i>
          {{ connectionStatus }}
        </span>
        <span class="status-item">
          <i class="el-icon-time"></i>
          {{ currentTime }}
        </span>
      </div>
      <div class="status-right">
        <span class="status-item">
          模式: {{ chatModeText }}
        </span>
        <span class="status-item">
          消息: {{ messageCount }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import MaxKBChat from '@/components/MaxKBChat.vue'
import { getApplicationInfo } from '@/api/maxkb'

export default {
  name: 'DemoAI',
  components: {
    MaxKBChat
  },
  data() {
    return {
      chatMode: 'normal', // normal, knowledge, analysis
      isFullscreen: false,
      useStream: true,
      showTimestamp: true,
      autoScroll: true,
      maxMessages: 100,
      messageCount: 0,
      connectionStatus: '已连接',
      currentTime: '',
      timeInterval: null,

      // 不同模式的配置
      chatConfigs: {
        normal: {
          title: 'AI智能助手',
          welcomeTitle: '欢迎使用AI智能助手',
          welcomeText: '我是您的智能助手，可以回答各种问题，协助您完成工作。请输入您的问题开始对话。',
          placeholder: '请输入您的问题...',
          quickQuestions: [
            '你好，请介绍一下自己',
            '你能帮我做什么？',
            '如何使用这个系统？',
            '请推荐一些学习资源'
          ]
        },
        knowledge: {
          title: '知识库问答',
          welcomeTitle: '知识库智能问答',
          welcomeText: '我可以基于知识库为您提供准确的信息检索和问答服务。请输入您想了解的内容。',
          placeholder: '请输入您想查询的知识...',
          quickQuestions: [
            '系统有哪些功能模块？',
            '如何进行企业管理？',
            '数据安全如何保障？',
            '系统支持哪些操作？'
          ]
        },
        analysis: {
          title: '数据分析助手',
          welcomeTitle: '智能数据分析',
          welcomeText: '我可以帮助您分析数据、生成报告和提供业务洞察。请描述您的分析需求。',
          placeholder: '请描述您的数据分析需求...',
          quickQuestions: [
            '分析企业风险分布情况',
            '生成月度数据报告',
            '对比不同区域的数据',
            '预测未来发展趋势'
          ]
        }
      },

      // 示例问题
      exampleQuestions: {
        normal: [
          '什么是人工智能？',
          '如何提高工作效率？',
          '推荐一些好用的工具',
          '解释一下机器学习',
          '如何学习编程？'
        ],
        knowledge: [
          '企业档案管理流程',
          '风险评估标准',
          '合规检查要求',
          '数据备份策略',
          '系统操作指南'
        ],
        analysis: [
          '企业数量变化趋势',
          '风险等级分布分析',
          '区域发展对比',
          '合规率统计分析',
          '资产增长预测'
        ]
      }
    }
  },
  computed: {
    currentChatTitle() {
      return this.chatConfigs[this.chatMode].title
    },
    currentWelcomeTitle() {
      return this.chatConfigs[this.chatMode].welcomeTitle
    },
    currentWelcomeText() {
      return this.chatConfigs[this.chatMode].welcomeText
    },
    currentPlaceholder() {
      return this.chatConfigs[this.chatMode].placeholder
    },
    currentQuickQuestions() {
      return this.chatConfigs[this.chatMode].quickQuestions
    },
    currentExamples() {
      return this.exampleQuestions[this.chatMode]
    },
    chatModeText() {
      const modeMap = {
        normal: '普通对话',
        knowledge: '知识库问答',
        analysis: '数据分析'
      }
      return modeMap[this.chatMode]
    }
  },
  mounted() {
    this.initTime()
    this.checkConnection()
  },
  beforeDestroy() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    // 设置聊天模式
    setChatMode(mode) {
      if (this.chatMode !== mode) {
        this.chatMode = mode
        this.$message.success(`已切换到${this.chatModeText}模式`)
        
        // 清空当前对话
        if (this.$refs.chatComponent) {
          this.$refs.chatComponent.clearChat()
        }
      }
    },

    // 处理全屏变化
    handleFullscreenChange(isFullscreen) {
      this.isFullscreen = isFullscreen
    },

    // 询问示例问题
    askExample(question) {
      if (this.$refs.chatComponent) {
        this.$refs.chatComponent.inputMessage = question
        this.$refs.chatComponent.sendMessage()
      }
    },

    // 初始化时间
    initTime() {
      this.updateTime()
      this.timeInterval = setInterval(this.updateTime, 1000)
    },

    // 更新时间
    updateTime() {
      this.currentTime = new Date().toLocaleTimeString('zh-CN')
    },

    // 检查连接状态
    async checkConnection() {
      try {
        await getApplicationInfo()
        this.connectionStatus = '已连接'
      } catch (error) {
        this.connectionStatus = '连接失败'
        console.error('连接maxKB失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-ai-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e1e8ed;
  padding: 20px 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;

    .header-left {
      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        color: #333;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #667eea;
        }
      }

      .page-description {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }
  }
}

.main-content {
  flex: 1;
  padding: 20px 24px;
  overflow: hidden;

  &.fullscreen {
    padding: 0;
  }

  .content-wrapper {
    display: flex;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
    gap: 20px;
  }
}

.sidebar {
  width: 280px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow-y: auto;

  .sidebar-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #333;
      display: flex;
      align-items: center;

      i {
        margin-right: 6px;
        color: #667eea;
      }
    }

    .feature-list {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        display: flex;
        align-items: center;
        padding: 6px 0;
        color: #666;
        font-size: 14px;

        i {
          margin-right: 8px;
          color: #67c23a;
          font-size: 12px;
        }
      }
    }

    .example-questions {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .example-button {
        text-align: left;
        padding: 8px 12px;
        border-radius: 6px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        color: #495057;
        font-size: 13px;
        line-height: 1.4;

        &:hover {
          background: #e9ecef;
          color: #667eea;
        }
      }
    }

    .settings {
      .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        font-size: 14px;
        color: #666;
      }
    }
  }
}

.chat-area {
  flex: 1;
  min-width: 0;
}

.status-bar {
  background: white;
  border-top: 1px solid #e1e8ed;
  padding: 8px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;

  .status-left,
  .status-right {
    display: flex;
    gap: 16px;
  }

  .status-item {
    display: flex;
    align-items: center;

    i {
      margin-right: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .sidebar {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px 16px;

    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;

      .header-left .page-title {
        font-size: 20px;
      }
    }
  }

  .main-content {
    padding: 16px;

    .content-wrapper {
      flex-direction: column;
      gap: 16px;
    }
  }

  .sidebar {
    width: 100%;
    order: 2;
  }

  .chat-area {
    order: 1;
  }

  .status-bar {
    padding: 8px 16px;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;

    .status-left,
    .status-right {
      gap: 12px;
    }
  }
}
</style>
