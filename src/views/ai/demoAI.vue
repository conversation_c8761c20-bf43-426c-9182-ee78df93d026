<template>
  <div class="demo-ai-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <i class="el-icon-chat-dot-round"></i>
            AI智能助手演示
          </h1>
          <p class="page-description">
            基于maxKB构建的智能问答系统，支持自然语言对话、知识库检索和智能推理
          </p>
        </div>
        <div class="header-right">
          <el-button-group>
            <el-button 
              :type="chatMode === 'normal' ? 'primary' : 'default'"
              @click="setChatMode('normal')"
              icon="el-icon-chat-line-round"
            >
              普通对话
            </el-button>
            <el-button 
              :type="chatMode === 'knowledge' ? 'primary' : 'default'"
              @click="setChatMode('knowledge')"
              icon="el-icon-collection"
            >
              知识库问答
            </el-button>
            <el-button 
              :type="chatMode === 'analysis' ? 'primary' : 'default'"
              @click="setChatMode('analysis')"
              icon="el-icon-data-analysis"
            >
              数据分析
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" :class="{ 'fullscreen': isFullscreen }">
      <div class="content-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" v-show="!isFullscreen">
          <!-- 新建对话按钮 -->
          <div class="sidebar-section">
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="createNewConversation"
              class="new-chat-btn"
              :loading="isCreatingChat"
            >
              新建对话
            </el-button>
          </div>

          <!-- 历史记录 -->
          <div class="sidebar-section">
            <h3 class="section-title">
              <i class="el-icon-time"></i>
              历史记录
              <el-button
                type="text"
                size="mini"
                icon="el-icon-refresh"
                @click="refreshHistory"
                class="refresh-btn"
                :loading="isRefreshingHistory"
              ></el-button>
            </h3>

            <!-- 搜索框 -->
            <div class="search-box">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索对话..."
                size="small"
                prefix-icon="el-icon-search"
                clearable
                @input="filterHistory"
              ></el-input>
            </div>

            <!-- 历史记录列表 -->
            <div class="history-list">
              <div v-if="filteredHistory.length === 0" class="empty-history">
                <i class="el-icon-chat-line-round"></i>
                <p>{{ searchKeyword ? '未找到相关对话' : '暂无历史记录' }}</p>
              </div>

              <div
                v-for="conversation in filteredHistory"
                :key="conversation.id"
                :class="['history-item', { 'active': currentConversationId === conversation.id }]"
                @click="loadConversation(conversation)"
              >
                <div class="conversation-info">
                  <div class="conversation-title">
                    {{ conversation.title || '新对话' }}
                  </div>
                  <div class="conversation-preview">
                    {{ conversation.lastMessage || '开始新的对话...' }}
                  </div>
                  <div class="conversation-time">
                    {{ formatTime(conversation.updatedAt) }}
                  </div>
                </div>
                <div class="conversation-actions">
                  <el-dropdown trigger="click" @command="handleConversationAction">
                    <el-button type="text" size="mini" icon="el-icon-more"></el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="{action: 'rename', id: conversation.id}">
                        <i class="el-icon-edit"></i> 重命名
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'delete', id: conversation.id}" divided>
                        <i class="el-icon-delete"></i> 删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area">
          <div class="chat-container">
            <!-- 聊天头部 -->
            <div class="chat-header">
              <div class="header-left">
                <i class="el-icon-chat-dot-round"></i>
                <span class="chat-title">{{ currentChatTitle }}</span>
                <div class="connection-status">
                  <i
                    :class="connectionStatus === '已连接' ? 'el-icon-success' : 'el-icon-warning'"
                    :style="{ color: connectionStatus === '已连接' ? '#67c23a' : '#e6a23c' }"
                  ></i>
                  <span class="status-text">{{ connectionStatus }}</span>
                </div>
              </div>
              <div class="header-right">
                <el-button
                  type="text"
                  icon="el-icon-refresh"
                  @click="clearCurrentChat"
                  title="清空对话"
                ></el-button>
                <el-button
                  type="text"
                  :icon="isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'"
                  @click="toggleFullscreen"
                  :title="isFullscreen ? '退出全屏' : '全屏'"
                ></el-button>
              </div>
            </div>

            <!-- 聊天消息区域 -->
            <div class="chat-messages" ref="messagesContainer">
              <div class="messages-wrapper">
                <!-- 欢迎消息 -->
                <div v-if="currentMessages.length === 0" class="welcome-message">
                  <div class="welcome-content">
                    <i class="el-icon-chat-dot-round welcome-icon"></i>
                    <h3>{{ currentWelcomeTitle }}</h3>
                    <p>{{ currentWelcomeText }}</p>
                    <div class="quick-questions" v-if="currentQuickQuestions.length > 0">
                      <h4>快速提问：</h4>
                      <div class="question-buttons">
                        <el-button
                          v-for="(question, index) in currentQuickQuestions"
                          :key="index"
                          size="small"
                          type="primary"
                          plain
                          @click="sendQuickQuestion(question)"
                        >
                          {{ question }}
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 消息列表 -->
                <div
                  v-for="(message, index) in currentMessages"
                  :key="index"
                  :class="['message-item', message.role]"
                >
                  <div class="message-avatar">
                    <i :class="message.role === 'user' ? 'el-icon-user' : 'el-icon-chat-dot-round'"></i>
                  </div>
                  <div class="message-content">
                    <div class="message-text" v-html="formatMessage(message.content)"></div>
                    <div class="message-time">{{ formatMessageTime(message.timestamp) }}</div>
                  </div>
                </div>

                <!-- 正在输入指示器 -->
                <div v-if="isTyping" class="message-item assistant typing">
                  <div class="message-avatar">
                    <i class="el-icon-chat-dot-round"></i>
                  </div>
                  <div class="message-content">
                    <div class="typing-indicator">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input">
              <div class="input-wrapper">
                <el-input
                  v-model="inputMessage"
                  type="textarea"
                  :rows="inputRows"
                  :placeholder="currentPlaceholder"
                  :disabled="isLoading"
                  @keydown.enter.exact="handleEnterKey"
                  @keydown.enter.shift.exact="handleShiftEnter"
                  ref="messageInput"
                  resize="none"
                  maxlength="2000"
                  show-word-limit
                ></el-input>
                <div class="input-actions">
                  <el-button
                    type="primary"
                    :loading="isLoading"
                    :disabled="!inputMessage.trim()"
                    @click="sendUserMessage"
                    icon="el-icon-s-promotion"
                  >
                    发送
                  </el-button>
                </div>
              </div>
              <div class="input-tips">
                <span class="tip">按 Enter 发送，Shift + Enter 换行</span>
                <span class="tip">{{ inputMessage.length }}/2000</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar" v-show="!isFullscreen">
      <div class="status-left">
        <span class="status-item">
          <i class="el-icon-connection"></i>
          {{ connectionStatus }}
        </span>
        <span class="status-item">
          <i class="el-icon-time"></i>
          {{ currentTime }}
        </span>
      </div>
      <div class="status-right">
        <span class="status-item">
          模式: {{ chatModeText }}
        </span>
        <span class="status-item">
          消息: {{ messageCount }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { getApplicationProfile, sendMessage, createConversation } from '@/api/maxkb'

export default {
  name: 'DemoAI',
  components: {},
  data() {
    return {
      chatMode: 'normal', // normal, knowledge, analysis
      isFullscreen: false,
      useStream: true,
      maxMessages: 100,
      messageCount: 0,
      connectionStatus: '已连接',
      currentTime: '',
      timeInterval: null,

      // 历史记录相关
      conversations: [], // 所有对话历史
      filteredHistory: [], // 过滤后的历史记录
      currentConversationId: null, // 当前对话ID
      searchKeyword: '', // 搜索关键词
      isCreatingChat: false, // 是否正在创建新对话
      isRefreshingHistory: false, // 是否正在刷新历史记录

      // 聊天功能相关
      currentMessages: [], // 当前对话的消息列表
      inputMessage: '', // 输入框内容
      isLoading: false, // 是否正在发送消息
      isTyping: false, // AI是否正在输入
      inputRows: 1, // 输入框行数
      conversationId: null, // 当前会话ID

      // 不同模式的配置
      chatConfigs: {
        normal: {
          title: 'AI智能助手',
          welcomeTitle: '欢迎使用AI智能助手',
          welcomeText: '我是您的智能助手，可以回答各种问题，协助您完成工作。请输入您的问题开始对话。',
          placeholder: '请输入您的问题...',
          quickQuestions: [
            '你好，请介绍一下自己',
            '你能帮我做什么？',
            '如何使用这个系统？',
            '请推荐一些学习资源'
          ]
        },
        knowledge: {
          title: '知识库问答',
          welcomeTitle: '知识库智能问答',
          welcomeText: '我可以基于知识库为您提供准确的信息检索和问答服务。请输入您想了解的内容。',
          placeholder: '请输入您想查询的知识...',
          quickQuestions: [
            '系统有哪些功能模块？',
            '如何进行企业管理？',
            '数据安全如何保障？',
            '系统支持哪些操作？'
          ]
        },
        analysis: {
          title: '数据分析助手',
          welcomeTitle: '智能数据分析',
          welcomeText: '我可以帮助您分析数据、生成报告和提供业务洞察。请描述您的分析需求。',
          placeholder: '请描述您的数据分析需求...',
          quickQuestions: [
            '分析企业风险分布情况',
            '生成月度数据报告',
            '对比不同区域的数据',
            '预测未来发展趋势'
          ]
        }
      }
    }
  },
  computed: {
    currentChatTitle() {
      return this.chatConfigs[this.chatMode].title
    },
    currentWelcomeTitle() {
      return this.chatConfigs[this.chatMode].welcomeTitle
    },
    currentWelcomeText() {
      return this.chatConfigs[this.chatMode].welcomeText
    },
    currentPlaceholder() {
      return this.chatConfigs[this.chatMode].placeholder
    },
    currentQuickQuestions() {
      return this.chatConfigs[this.chatMode].quickQuestions
    },
    chatModeText() {
      const modeMap = {
        normal: '普通对话',
        knowledge: '知识库问答',
        analysis: '数据分析'
      }
      return modeMap[this.chatMode]
    }
  },
  mounted() {
    this.initTime()
    this.checkConnection()
    this.loadConversationHistory()

    // 如果没有历史记录，创建新对话
    this.$nextTick(() => {
      if (this.conversations.length === 0) {
        this.createNewConversation()
      } else {
        // 如果有历史记录，选择最新的对话
        this.currentConversationId = this.conversations[0].id
      }
    })
  },
  beforeDestroy() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    // 设置聊天模式
    setChatMode(mode) {
      if (this.chatMode !== mode) {
        this.chatMode = mode
        this.$message.success(`已切换到${this.chatModeText}模式`)

        // 清空当前对话
        this.currentMessages = []
        this.inputMessage = ''
      }
    },

    // 处理全屏变化
    handleFullscreenChange(isFullscreen) {
      this.isFullscreen = isFullscreen
    },

    // 初始化时间
    initTime() {
      this.updateTime()
      this.timeInterval = setInterval(this.updateTime, 1000)
    },

    // 更新时间
    updateTime() {
      this.currentTime = new Date().toLocaleTimeString('zh-CN')
    },

    // 检查连接状态
    async checkConnection() {
      try {
        const response = await getApplicationProfile()
        this.connectionStatus = '已连接'
        console.log('maxKB连接成功:', response)

        // 显示连接成功消息和应用信息
        if (response && response.name) {
          this.$message.success(`AI助手服务连接成功: ${response.name}`)
        } else {
          this.$message.success('AI助手服务连接成功')
        }
      } catch (error) {
        this.connectionStatus = '连接失败'
        console.error('连接maxKB失败:', error)

        // 显示连接失败警告
        this.$message.warning('AI助手服务连接失败，将使用离线模式')
      }
    },

    // 创建新对话
    async createNewConversation() {
      this.isCreatingChat = true
      try {
        const newConversation = {
          id: this.generateId(),
          title: '',
          lastMessage: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          mode: this.chatMode,
          messages: []
        }

        this.conversations.unshift(newConversation)
        this.currentConversationId = newConversation.id
        this.saveConversationHistory()
        this.filterHistory()

        // 清空当前消息
        this.currentMessages = []
        this.inputMessage = ''
        this.conversationId = null

        // 为新对话创建maxKB会话
        await this.createMaxKBConversation()

        this.$message.success('新对话已创建')
      } catch (error) {
        this.$message.error('创建对话失败')
        console.error('创建对话失败:', error)
      } finally {
        this.isCreatingChat = false
      }
    },

    // 加载对话历史
    loadConversationHistory() {
      try {
        const saved = localStorage.getItem('ai_conversations')
        if (saved) {
          this.conversations = JSON.parse(saved).map(conv => ({
            ...conv,
            createdAt: new Date(conv.createdAt),
            updatedAt: new Date(conv.updatedAt)
          }))
        }
        this.filterHistory()
      } catch (error) {
        console.error('加载对话历史失败:', error)
        this.conversations = []
      }
    },

    // 保存对话历史
    saveConversationHistory() {
      try {
        localStorage.setItem('ai_conversations', JSON.stringify(this.conversations))
      } catch (error) {
        console.error('保存对话历史失败:', error)
      }
    },

    // 刷新历史记录
    async refreshHistory() {
      this.isRefreshingHistory = true
      try {
        // 模拟刷新延迟
        await new Promise(resolve => setTimeout(resolve, 500))
        this.loadConversationHistory()
        this.$message.success('历史记录已刷新')
      } catch (error) {
        this.$message.error('刷新失败')
      } finally {
        this.isRefreshingHistory = false
      }
    },

    // 过滤历史记录
    filterHistory() {
      if (!this.searchKeyword.trim()) {
        this.filteredHistory = [...this.conversations]
      } else {
        const keyword = this.searchKeyword.toLowerCase()
        this.filteredHistory = this.conversations.filter(conv =>
          (conv.title && conv.title.toLowerCase().includes(keyword)) ||
          (conv.lastMessage && conv.lastMessage.toLowerCase().includes(keyword))
        )
      }
    },

    // 加载对话
    loadConversation(conversation) {
      if (!conversation || !conversation.id) {
        console.error('无效的对话对象:', conversation)
        return
      }

      this.currentConversationId = conversation.id
      this.chatMode = conversation.mode || 'normal'

      // 加载对话消息
      if (conversation.messages && Array.isArray(conversation.messages)) {
        this.currentMessages = [...conversation.messages]
      } else {
        this.currentMessages = []
      }

      this.$message.success(`已加载对话: ${conversation.title || '新对话'}`)
    },

    // 处理对话操作
    handleConversationAction(command) {
      if (!command || typeof command !== 'object') {
        console.error('无效的命令对象:', command)
        return
      }

      const { action, id } = command
      if (!action || !id) {
        console.error('缺少必要的参数:', { action, id })
        return
      }

      const conversation = this.conversations.find(conv => conv.id === id)
      if (!conversation) {
        console.error('未找到对话:', id)
        return
      }

      switch (action) {
        case 'rename':
          this.renameConversation(conversation)
          break
        case 'delete':
          this.deleteConversation(conversation)
          break
        default:
          console.error('未知的操作:', action)
      }
    },

    // 重命名对话
    renameConversation(conversation) {
      this.$prompt('请输入新的对话名称', '重命名对话', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: conversation.title || '新对话',
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '对话名称不能为空'
          }
          return true
        }
      }).then(({ value }) => {
        conversation.title = value.trim()
        conversation.updatedAt = new Date()
        this.saveConversationHistory()
        this.filterHistory()
        this.$message.success('重命名成功')
      }).catch(() => {})
    },

    // 删除对话
    deleteConversation(conversation) {
      this.$confirm(`确定要删除对话"${conversation.title || '新对话'}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.conversations.findIndex(conv => conv.id === conversation.id)
        if (index > -1) {
          this.conversations.splice(index, 1)

          // 如果删除的是当前对话，创建新对话
          if (this.currentConversationId === conversation.id) {
            this.createNewConversation()
          }

          this.saveConversationHistory()
          this.filterHistory()
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },

    // 格式化时间
    formatTime(date) {
      if (!date) return ''

      const now = new Date()
      const time = new Date(date)
      const diff = now - time

      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 24小时内
        return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else if (diff < 604800000) { // 7天内
        const days = Math.floor(diff / 86400000)
        return `${days}天前`
      } else {
        return time.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
      }
    },

    // 生成唯一ID
    generateId() {
      return 'conv_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    },

    // 聊天功能方法
    // 发送消息
    async sendUserMessage() {
      if (!this.inputMessage.trim() || this.isLoading) return

      const userMessage = this.inputMessage.trim()
      this.inputMessage = ''
      this.adjustInputHeight()

      // 添加用户消息
      this.addMessage('user', userMessage)

      this.isLoading = true
      this.isTyping = true

      try {
        // 模拟AI回复
        await this.simulateAIResponse(userMessage)
      } catch (error) {
        console.error('发送消息失败:', error)
        this.addMessage('assistant', '抱歉，我遇到了一些问题，请稍后再试。')
        this.$message.error('发送消息失败')
      } finally {
        this.isLoading = false
        this.isTyping = false
      }
    },

    // 调用maxKB AI回复
    async simulateAIResponse(userMessage) {
      try {
        // 如果没有会话ID，先创建会话
        if (!this.conversationId) {
          await this.createMaxKBConversation()
        }

        // 调用maxKB API发送消息 (步骤3: 使用chat_id进行对话)
        console.log('[demoAI] 准备发送消息:', {
          message: userMessage,
          chat_id: this.conversationId,
          conversationIdType: typeof this.conversationId
        })

        const response = await sendMessage({
          message: userMessage,
          chat_id: this.conversationId,
          re_chat: false,
          stream: true
        })

        // 处理API响应 (根据maxKB文档格式)
        if (response) {
          // maxKB API可能返回不同的响应格式，需要适配
          let content = ''

          if (typeof response === 'string') {
            content = response
          } else if (response.content) {
            content = response.content
          } else if (response.answer) {
            content = response.answer
          } else if (response.message) {
            content = response.message
          } else if (response.data && response.data.content) {
            content = response.data.content
          } else {
            content = '抱歉，我暂时无法理解您的问题，请稍后再试。'
          }

          this.addMessage('assistant', content)
        } else {
          this.addMessage('assistant', '抱歉，我暂时无法理解您的问题，请稍后再试。')
        }
      } catch (error) {
        console.error('调用maxKB API失败:', error)

        // API调用失败时的降级处理
        let fallbackResponse = ''

        if (userMessage.includes('你好') || userMessage.includes('hello')) {
          fallbackResponse = '你好！我是AI智能助手，很高兴为您服务。有什么我可以帮助您的吗？'
        } else if (userMessage.includes('介绍') || userMessage.includes('自己')) {
          fallbackResponse = '我是基于maxKB构建的AI智能助手，可以进行自然语言对话、知识库检索和智能推理。'
        } else if (userMessage.includes('功能') || userMessage.includes('能做什么')) {
          fallbackResponse = '我的主要功能包括：\n1. 智能对话交互\n2. 知识库检索\n3. 数据分析协助\n4. 问题解答\n5. 信息整理'
        } else {
          fallbackResponse = '抱歉，我遇到了一些技术问题，请稍后再试。如果问题持续存在，请联系管理员。'
        }

        this.addMessage('assistant', fallbackResponse)

        // 显示错误提示
        this.$message.warning('AI服务暂时不可用，已切换到离线模式')
      }
    },

    // 创建maxKB会话 (按照正确的API流程)
    async createMaxKBConversation() {
      try {
        console.log('[maxKB] 开始创建会话...')
        const response = await createConversation()

        if (response && response.id) {
          this.conversationId = response.id
          console.log('[maxKB] 会话创建成功:', {
            chatId: response.id,
            applicationId: response.applicationId,
            applicationName: response.applicationName
          })

          // 显示应用信息
          if (response.applicationName) {
            this.$message.success(`已连接到: ${response.applicationName}`)
          }
        } else {
          throw new Error('创建会话失败: 无效的响应')
        }
      } catch (error) {
        console.error('[maxKB] 创建会话失败:', error)
        this.$message.error(`创建会话失败: ${error.message}`)

        // 生成本地会话ID作为降级方案
        this.conversationId = 'local_' + Date.now()
        console.log('[maxKB] 使用本地会话ID:', this.conversationId)
      }
    },

    // 添加消息
    addMessage(role, content) {
      const message = {
        role,
        content,
        timestamp: new Date()
      }

      this.currentMessages.push(message)

      // 更新当前对话的消息
      this.updateCurrentConversation(message)

      this.$nextTick(() => {
        this.scrollToBottom()
      })

      return this.currentMessages.length - 1
    },

    // 更新当前对话
    updateCurrentConversation(message) {
      if (this.currentConversationId) {
        const conversation = this.conversations.find(conv => conv.id === this.currentConversationId)
        if (conversation) {
          if (!conversation.messages) {
            conversation.messages = []
          }
          conversation.messages.push(message)
          conversation.lastMessage = message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '')
          conversation.updatedAt = new Date()

          // 如果对话没有标题，使用第一条用户消息作为标题
          if (!conversation.title && message.role === 'user') {
            conversation.title = message.content.substring(0, 20) + (message.content.length > 20 ? '...' : '')
          }

          this.saveConversationHistory()
          this.filterHistory()
        }
      }
    },

    // 快速提问
    sendQuickQuestion(question) {
      this.inputMessage = question
      this.sendUserMessage()
    },

    // 清空当前聊天
    clearCurrentChat() {
      this.$confirm('确定要清空当前对话记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.currentMessages = []
        this.conversationId = null
        this.$message.success('对话已清空')
      }).catch(() => {})
    },

    // 切换全屏
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      this.handleFullscreenChange(this.isFullscreen)
    },

    // 处理Enter键
    handleEnterKey(event) {
      if (!event.shiftKey) {
        event.preventDefault()
        this.sendUserMessage()
      }
    },

    // 处理Shift+Enter
    handleShiftEnter() {
      // 允许换行
    },

    // 调整输入框高度
    adjustInputHeight() {
      this.$nextTick(() => {
        const lines = this.inputMessage.split('\n').length
        this.inputRows = Math.min(Math.max(lines, 1), 4)
      })
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messagesContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },

    // 格式化消息
    formatMessage(content) {
      // 简单的文本格式化，将换行转换为<br>
      return content.replace(/\n/g, '<br>')
    },

    // 格式化消息时间
    formatMessageTime(timestamp) {
      const now = new Date()
      const time = new Date(timestamp)
      const diff = now - time

      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 24小时内
        return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else {
        return time.toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      }
    }
  },
  watch: {
    inputMessage() {
      this.adjustInputHeight()
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-ai-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e1e8ed;
  padding: 20px 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;

    .header-left {
      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        color: #333;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #667eea;
        }
      }

      .page-description {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }
  }
}

.main-content {
  flex: 1;
  padding: 20px 24px;
  overflow: hidden;

  &.fullscreen {
    padding: 0;
  }

  .content-wrapper {
    display: flex;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
    gap: 20px;
  }
}

.sidebar {
  width: 280px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow-y: auto;

  .sidebar-section {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #333;
      display: flex;
      align-items: center;
      justify-content: space-between;

      i {
        margin-right: 6px;
        color: #667eea;
      }

      .refresh-btn {
        padding: 0;
        margin-left: auto;
        color: #999;

        &:hover {
          color: #667eea;
        }
      }
    }

    .new-chat-btn {
      width: 100%;
      height: 40px;
      font-size: 14px;
      border-radius: 8px;
    }

    .search-box {
      margin-bottom: 12px;
    }

    .history-list {
      max-height: 400px;
      overflow-y: auto;

      .empty-history {
        text-align: center;
        padding: 40px 20px;
        color: #999;

        i {
          font-size: 32px;
          margin-bottom: 8px;
          display: block;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }

      .history-item {
        display: flex;
        align-items: flex-start;
        padding: 12px;
        margin-bottom: 8px;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: #667eea;
          background: #f8f9ff;
        }

        &.active {
          border-color: #667eea;
          background: #f0f4ff;
        }

        .conversation-info {
          flex: 1;
          min-width: 0;

          .conversation-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .conversation-preview {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .conversation-time {
            font-size: 11px;
            color: #999;
          }
        }

        .conversation-actions {
          flex-shrink: 0;
          margin-left: 8px;
          opacity: 0;
          transition: opacity 0.2s ease;

          .el-button {
            padding: 4px;
            color: #999;

            &:hover {
              color: #667eea;
            }
          }
        }

        &:hover .conversation-actions {
          opacity: 1;
        }
      }
    }
  }
}

.chat-area {
  flex: 1;
  min-width: 0;

  .chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: 1px solid #e1e8ed;

    .header-left {
      display: flex;
      align-items: center;

      i {
        font-size: 20px;
        margin-right: 8px;
      }

      .chat-title {
        font-size: 16px;
        font-weight: 600;
        margin-right: 16px;
      }

      .connection-status {
        display: flex;
        align-items: center;
        font-size: 12px;
        opacity: 0.9;

        i {
          font-size: 14px;
          margin-right: 4px;
        }

        .status-text {
          font-size: 12px;
        }
      }
    }

    .header-right {
      .el-button {
        color: white;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;

    .messages-wrapper {
      max-width: 800px;
      margin: 0 auto;
    }

    .welcome-message {
      text-align: center;
      padding: 40px 20px;

      .welcome-content {
        .welcome-icon {
          font-size: 48px;
          color: #667eea;
          margin-bottom: 16px;
        }

        h3 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 24px;
        }

        p {
          margin: 0 0 24px 0;
          color: #666;
          line-height: 1.6;
        }

        .quick-questions {
          h4 {
            margin: 0 0 12px 0;
            color: #333;
            font-size: 16px;
          }

          .question-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;

            .el-button {
              margin: 0;
            }
          }
        }
      }
    }

    .message-item {
      display: flex;
      margin-bottom: 20px;
      animation: fadeIn 0.3s ease-in;

      &.user {
        flex-direction: row-reverse;

        .message-avatar {
          margin-left: 12px;
          margin-right: 0;
          background: #667eea;
        }

        .message-content {
          background: #667eea;
          color: white;
          border-radius: 18px 18px 4px 18px;
        }
      }

      &.assistant {
        .message-avatar {
          margin-right: 12px;
          background: #f1f3f4;
          color: #666;
        }

        .message-content {
          background: white;
          border-radius: 18px 18px 18px 4px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }

      .message-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        i {
          font-size: 16px;
        }
      }

      .message-content {
        max-width: 70%;
        padding: 12px 16px;
        position: relative;

        .message-text {
          line-height: 1.5;
          word-wrap: break-word;
        }

        .message-time {
          font-size: 11px;
          opacity: 0.7;
          margin-top: 4px;
        }
      }

      &.typing {
        .typing-indicator {
          display: flex;
          align-items: center;
          gap: 4px;

          span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #999;
            animation: typing 1.4s infinite ease-in-out;

            &:nth-child(1) { animation-delay: -0.32s; }
            &:nth-child(2) { animation-delay: -0.16s; }
            &:nth-child(3) { animation-delay: 0s; }
          }
        }
      }
    }
  }

  .chat-input {
    padding: 20px;
    background: white;
    border-top: 1px solid #e1e8ed;

    .input-wrapper {
      display: flex;
      gap: 12px;
      align-items: flex-end;

      .el-textarea {
        flex: 1;
      }

      .input-actions {
        .el-button {
          height: 40px;
        }
      }
    }

    .input-tips {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      font-size: 12px;
      color: #999;
    }
  }
}

.status-bar {
  background: white;
  border-top: 1px solid #e1e8ed;
  padding: 8px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;

  .status-left,
  .status-right {
    display: flex;
    gap: 16px;
  }

  .status-item {
    display: flex;
    align-items: center;

    i {
      margin-right: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .sidebar {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px 16px;

    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;

      .header-left .page-title {
        font-size: 20px;
      }
    }
  }

  .main-content {
    padding: 16px;

    .content-wrapper {
      flex-direction: column;
      gap: 16px;
    }
  }

  .sidebar {
    width: 100%;
    order: 2;
  }

  .chat-area {
    order: 1;
  }

  .status-bar {
    padding: 8px 16px;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;

    .status-left,
    .status-right {
      gap: 12px;
    }
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}
</style>
